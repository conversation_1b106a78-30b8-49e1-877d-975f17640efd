using UnityEngine;

/// <summary>
/// 角色动画控制器 - 专门用于控制ffu.fbx等角色模型的动画
/// </summary>
public class CharacterAnimationController : MonoBehaviour
{
    [Header("动画设置")]
    public Animator animator;
    public bool enableAnimations = true;
    
    [Header("动画参数")]
    [Tooltip("移动速度参数名")]
    public string speedParameterName = "Speed";
    [Tooltip("是否跑步参数名")]
    public string isRunningParameterName = "IsRunning";
    [Tooltip("是否在地面参数名")]
    public string isGroundedParameterName = "IsGrounded";
    
    [Header("动画触发器")]
    [Tooltip("待机动画触发器")]
    public string idleTrigger = "Idle";
    [Tooltip("行走动画触发器")]
    public string walkTrigger = "Walk";
    [Tooltip("跑步动画触发器")]
    public string runTrigger = "Run";
    [<PERSON>lt<PERSON>("跳跃动画触发器")]
    public string jumpTrigger = "Jump";
    
    [Header("动画平滑")]
    [Tooltip("动画过渡平滑度")]
    public float animationSmoothTime = 0.1f;
    
    // 私有变量
    private float currentSpeed = 0f;
    private bool wasGrounded = true;
    
    void Start()
    {
        // 如果没有指定Animator，尝试自动获取
        if (animator == null)
        {
            animator = GetComponent<Animator>();
            if (animator == null)
            {
                animator = GetComponentInChildren<Animator>();
            }
        }
        
        if (animator == null)
        {
            Debug.LogWarning($"CharacterAnimationController: 在 {gameObject.name} 上没有找到Animator组件！");
            enableAnimations = false;
        }
        else
        {
            Debug.Log($"CharacterAnimationController: 已找到Animator组件，动画系统已启用");
        }
    }
    
    /// <summary>
    /// 更新移动动画
    /// </summary>
    /// <param name="movementSpeed">移动速度 (0-1)</param>
    /// <param name="isRunning">是否在跑步</param>
    /// <param name="isGrounded">是否在地面上</param>
    public void UpdateMovementAnimation(float movementSpeed, bool isRunning, bool isGrounded)
    {
        if (!enableAnimations || animator == null)
        {
            Debug.LogWarning($"动画更新被跳过 - EnableAnimations: {enableAnimations}, Animator: {animator != null}");
            return;
        }

        // 平滑过渡速度值
        currentSpeed = Mathf.Lerp(currentSpeed, movementSpeed, Time.deltaTime / animationSmoothTime);

        // 只在速度变化较大时输出调试信息
        if (Mathf.Abs(movementSpeed - currentSpeed) > 0.1f)
        {
            Debug.Log($"动画状态变化 - Speed: {movementSpeed:F2} -> {currentSpeed:F2}, Running: {isRunning}");
        }

        // 设置动画参数
        SetAnimatorFloat(speedParameterName, currentSpeed);
        SetAnimatorBool(isRunningParameterName, isRunning);
        SetAnimatorBool(isGroundedParameterName, isGrounded);

        // 根据移动状态触发相应动画
        if (movementSpeed > 0.1f)
        {
            if (isRunning)
            {
                TriggerAnimation(runTrigger);
            }
            else
            {
                TriggerAnimation(walkTrigger);
            }
        }
        else
        {
            TriggerAnimation(idleTrigger);
        }

        // 检测着陆
        if (!wasGrounded && isGrounded)
        {
            OnLanded();
        }

        wasGrounded = isGrounded;
    }
    
    /// <summary>
    /// 播放跳跃动画
    /// </summary>
    public void PlayJumpAnimation()
    {
        if (!enableAnimations || animator == null) return;
        
        TriggerAnimation(jumpTrigger);
        Debug.Log("播放跳跃动画");
    }
    
    /// <summary>
    /// 播放指定动画
    /// </summary>
    /// <param name="animationName">动画状态名称</param>
    public void PlayAnimation(string animationName)
    {
        if (!enableAnimations || animator == null) return;
        
        if (HasAnimationState(animationName))
        {
            animator.Play(animationName);
            Debug.Log($"播放动画: {animationName}");
        }
        else
        {
            Debug.LogWarning($"动画状态 '{animationName}' 不存在！");
        }
    }
    
    /// <summary>
    /// 设置动画速度
    /// </summary>
    /// <param name="speed">动画播放速度</param>
    public void SetAnimationSpeed(float speed)
    {
        if (animator != null)
        {
            animator.speed = speed;
        }
    }
    
    /// <summary>
    /// 着陆时的处理
    /// </summary>
    private void OnLanded()
    {
        Debug.Log("角色着陆");
        // 可以在这里添加着陆音效或特效
    }
    
    /// <summary>
    /// 安全地设置Animator的Float参数
    /// </summary>
    private void SetAnimatorFloat(string parameterName, float value)
    {
        if (HasAnimatorParameter(parameterName))
        {
            animator.SetFloat(parameterName, value);
        }
        else
        {
            Debug.LogWarning($"动画参数 '{parameterName}' 不存在于Animator中！");
        }
    }
    
    /// <summary>
    /// 安全地设置Animator的Bool参数
    /// </summary>
    private void SetAnimatorBool(string parameterName, bool value)
    {
        if (HasAnimatorParameter(parameterName))
        {
            animator.SetBool(parameterName, value);
        }
    }
    
    /// <summary>
    /// 安全地触发Animator的Trigger
    /// </summary>
    private void TriggerAnimation(string triggerName)
    {
        if (HasAnimatorParameter(triggerName))
        {
            animator.SetTrigger(triggerName);
            Debug.Log($"成功触发动画触发器: {triggerName}");
        }
        else
        {
            Debug.LogWarning($"动画触发器 '{triggerName}' 不存在于Animator中！");
        }
    }
    
    /// <summary>
    /// 检查Animator是否有指定参数
    /// </summary>
    private bool HasAnimatorParameter(string parameterName)
    {
        if (animator == null || string.IsNullOrEmpty(parameterName)) return false;
        
        foreach (AnimatorControllerParameter param in animator.parameters)
        {
            if (param.name == parameterName)
                return true;
        }
        return false;
    }
    
    /// <summary>
    /// 检查Animator是否有指定动画状态
    /// </summary>
    private bool HasAnimationState(string stateName)
    {
        if (animator == null || string.IsNullOrEmpty(stateName)) return false;
        
        // 检查所有层的动画状态
        for (int i = 0; i < animator.layerCount; i++)
        {
            if (animator.HasState(i, Animator.StringToHash(stateName)))
                return true;
        }
        return false;
    }
    
    /// <summary>
    /// 获取当前动画状态信息
    /// </summary>
    public AnimatorStateInfo GetCurrentAnimationState(int layerIndex = 0)
    {
        if (animator != null && layerIndex < animator.layerCount)
        {
            return animator.GetCurrentAnimatorStateInfo(layerIndex);
        }
        return new AnimatorStateInfo();
    }
    
    /// <summary>
    /// 检查指定动画是否正在播放
    /// </summary>
    public bool IsAnimationPlaying(string animationName, int layerIndex = 0)
    {
        if (animator == null) return false;
        
        AnimatorStateInfo stateInfo = GetCurrentAnimationState(layerIndex);
        return stateInfo.IsName(animationName);
    }
}
