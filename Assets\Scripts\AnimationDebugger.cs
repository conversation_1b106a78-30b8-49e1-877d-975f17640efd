using UnityEngine;

/// <summary>
/// 动画调试器 - 帮助诊断动画问题
/// </summary>
public class AnimationDebugger : MonoBehaviour
{
    [Header("调试设置")]
    public bool enableDebug = true;
    public KeyCode debugKey = KeyCode.F1;
    
    void Update()
    {
        if (enableDebug && Input.GetKeyDown(debugKey))
        {
            DebugAnimationSystem();
        }
    }
    
    void DebugAnimationSystem()
    {
        Debug.Log("=== 动画系统调试信息 ===");
        
        // 检查FirstPersonController
        FirstPersonController fpc = FindObjectOfType<FirstPersonController>();
        if (fpc != null)
        {
            Debug.Log($"找到FirstPersonController: {fpc.gameObject.name}");
            Debug.Log($"启用动画: {fpc.enableAnimations}");
            Debug.Log($"使用简单动画: {fpc.useSimpleAnimation}");
            Debug.Log($"角色模型: {(fpc.characterModel != null ? fpc.characterModel.name : "未设置")}");
        }
        else
        {
            Debug.LogError("未找到FirstPersonController组件！");
        }
        
        // 检查所有Animator
        Animator[] animators = FindObjectsOfType<Animator>();
        Debug.Log($"场景中找到 {animators.Length} 个Animator组件:");
        foreach (Animator animator in animators)
        {
            Debug.Log($"- {animator.gameObject.name}: Controller = {(animator.runtimeAnimatorController != null ? animator.runtimeAnimatorController.name : "无")}");
        }
        
        // 检查CharacterAnimationController
        CharacterAnimationController[] animControllers = FindObjectsOfType<CharacterAnimationController>();
        Debug.Log($"场景中找到 {animControllers.Length} 个CharacterAnimationController组件:");
        foreach (CharacterAnimationController controller in animControllers)
        {
            Debug.Log($"- {controller.gameObject.name}: 启用 = {controller.enableAnimations}");
        }
        
        Debug.Log("=== 调试信息结束 ===");
    }
}
