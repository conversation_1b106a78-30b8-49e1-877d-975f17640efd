using UnityEngine;

public class FirstPersonController : Mono<PERSON><PERSON><PERSON>our
{
    [Header("Movement Settings")]
    public float walkSpeed = 5f;
    public float runSpeed = 10f;
    public float jumpHeight = 2f;
    public float gravity = -9.81f;
    
    [Header("Mouse Look Settings")]
    public float mouseSensitivity = 2f;
    public float maxLookAngle = 80f;

    [Header("Camera Settings")]
    public bool isThirdPerson = false;
    public Vector3 thirdPersonOffset = new Vector3(0, 2, -3);
    public KeyCode toggleViewKey = KeyCode.V;

    [Header("Character Model")]
    public GameObject characterModel;
    public bool hideModelInFirstPerson = true;

    [Header("Animation")]
    public bool enableAnimations = true;
    public bool useSimpleAnimation = false;

    [Header("Ground Check")]
    public Transform groundCheck;
    public float groundDistance = 0.4f;
    public LayerMask groundMask = 1;
    
    // Private variables
    private CharacterController controller;
    private Camera playerCamera;
    private Vector3 velocity;
    private bool isGrounded;
    private float xRotation = 0f;
    private bool isCursorLocked = true;
    private Animator characterAnimator;
    private SimpleCharacterAnimation simpleAnimation;
    private CharacterAnimationController animationController;
    
    void Start()
    {
        // Get components
        controller = GetComponent<CharacterController>();
        if (controller == null)
        {
            Debug.LogError($"CharacterController组件缺失！正在添加到 {gameObject.name}");
            controller = gameObject.AddComponent<CharacterController>();
            // 设置默认值
            controller.height = 2f;
            controller.radius = 0.5f;
            controller.center = new Vector3(0, 1f, 0);
        }

        playerCamera = GetComponentInChildren<Camera>();
        if (playerCamera == null)
        {
            Debug.LogError($"摄像机组件缺失！在 {gameObject.name} 中未找到Camera");
            // 尝试创建一个基本摄像机
            GameObject cameraObj = new GameObject("PlayerCamera");
            cameraObj.transform.SetParent(transform);
            cameraObj.transform.localPosition = new Vector3(0, 1.8f, 0);
            playerCamera = cameraObj.AddComponent<Camera>();
        }

        // Setup character model
        SetupCharacterModel();

        // 锁定鼠标光标
        Cursor.lockState = CursorLockMode.Locked;

        // Create ground check if it doesn't exist
        if (groundCheck == null)
        {
            GameObject groundCheckObj = new GameObject("GroundCheck");
            groundCheckObj.transform.SetParent(transform);
            groundCheckObj.transform.localPosition = new Vector3(0, -1f, 0);
            groundCheck = groundCheckObj.transform;
        }

        Debug.Log($"FirstPersonController初始化完成 - Controller: {controller != null}, Camera: {playerCamera != null}");
    }
    
    void Update()
    {
        // 处理ESC键切换鼠标锁定
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            isCursorLocked = !isCursorLocked;
            Cursor.lockState = isCursorLocked ? CursorLockMode.Locked : CursorLockMode.None;
        }

        HandleViewToggle();
        HandleMouseLook();
        HandleMovement();
        HandleJump();
    }
    
    void HandleMouseLook()
    {
        // Only handle mouse look if cursor is locked
        if (!isCursorLocked)
            return;

        // Get mouse input
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;

        // Rotate the player body left and right
        transform.Rotate(Vector3.up * mouseX);

        // Rotate the camera up and down
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -maxLookAngle, maxLookAngle);
        playerCamera.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
    }
    
    void HandleMovement()
    {
        // Check if grounded
        isGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);

        // 临时强制设置为着地状态进行测试
        if (!isGrounded)
        {
            isGrounded = true; // 临时修复
            Debug.Log("临时强制设置为着地状态");
        }

        // 调试地面检测
        Debug.Log($"Ground Check - Position: {groundCheck.position}, Distance: {groundDistance}, Grounded: {isGrounded}");

        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f; // Small negative value to keep grounded
        }

        // Get input
        float horizontal = Input.GetAxis("Horizontal"); // A/D keys
        float vertical = Input.GetAxis("Vertical");     // W/S keys

        // 调试输入
        Debug.Log($"Input - Horizontal: {horizontal}, Vertical: {vertical}");

        // Calculate movement direction
        Vector3 direction = transform.right * horizontal + transform.forward * vertical;

        // Determine speed (running or walking)
        bool isRunning = Input.GetKey(KeyCode.LeftShift);
        float currentSpeed = isRunning ? runSpeed : walkSpeed;

        // Move the character
        controller.Move(direction * currentSpeed * Time.deltaTime);

        // Update animations
        UpdateAnimations(direction, isRunning);
    }
    
    void HandleJump()
    {
        // Jump
        if (Input.GetButtonDown("Jump") && isGrounded)
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);

            // 播放跳跃动画
            if (enableAnimations && animationController != null)
            {
                animationController.PlayJumpAnimation();
            }
        }

        // Apply gravity
        velocity.y += gravity * Time.deltaTime;
        controller.Move(velocity * Time.deltaTime);
    }
    
    void UpdateAnimations(Vector3 movementDirection, bool isRunning)
    {
        if (!enableAnimations) return;

        // 计算移动速度 (0-1范围)
        float movementSpeed = movementDirection.magnitude;

        // 调试信息
        Debug.Log($"UpdateAnimations - Speed: {movementSpeed}, Running: {isRunning}, Grounded: {isGrounded}");
        Debug.Log($"UseSimpleAnimation: {useSimpleAnimation}, AnimationController: {animationController != null}, SimpleAnimation: {simpleAnimation != null}");

        if (useSimpleAnimation && simpleAnimation != null)
        {
            // 使用简单动画系统
            // SimpleCharacterAnimation会自动检测移动状态
            Debug.Log("使用简单动画系统");
        }
        else if (animationController != null)
        {
            // 使用复杂动画系统
            Debug.Log("使用复杂动画系统");
            animationController.UpdateMovementAnimation(movementSpeed, isRunning, isGrounded);
        }
        else
        {
            Debug.LogWarning("没有找到可用的动画系统！");
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw ground check sphere in scene view
        if (groundCheck != null)
        {
            Gizmos.color = isGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundDistance);
        }
    }

    void SetupCharacterModel()
    {
        if (characterModel != null)
        {
            // 确保角色模型是当前对象的子对象
            if (characterModel.transform.parent != transform)
            {
                characterModel.transform.SetParent(transform);
            }

            // 重置角色模型的本地位置和旋转
            characterModel.transform.localPosition = Vector3.zero;
            characterModel.transform.localRotation = Quaternion.identity;

            // 获取动画组件
            if (useSimpleAnimation)
            {
                simpleAnimation = characterModel.GetComponent<SimpleCharacterAnimation>();
                if (simpleAnimation == null)
                {
                    simpleAnimation = characterModel.AddComponent<SimpleCharacterAnimation>();
                }
            }
            else
            {
                characterAnimator = characterModel.GetComponent<Animator>();
                if (characterAnimator == null)
                {
                    characterAnimator = characterModel.GetComponentInChildren<Animator>();
                }

                Debug.Log($"找到Animator: {characterAnimator != null}");
                if (characterAnimator != null)
                {
                    Debug.Log($"Animator Controller: {characterAnimator.runtimeAnimatorController != null}");
                    if (characterAnimator.runtimeAnimatorController != null)
                    {
                        Debug.Log($"Controller名称: {characterAnimator.runtimeAnimatorController.name}");
                    }
                }

                // 获取或添加CharacterAnimationController
                animationController = characterModel.GetComponent<CharacterAnimationController>();
                if (animationController == null)
                {
                    animationController = characterModel.AddComponent<CharacterAnimationController>();
                    animationController.animator = characterAnimator;
                    Debug.Log("已添加CharacterAnimationController组件");
                }
                else
                {
                    Debug.Log("找到现有的CharacterAnimationController组件");
                }
            }

            // 根据当前视角模式设置角色可见性
            SetCharacterVisibility(!isThirdPerson && hideModelInFirstPerson);

            // 设置初始摄像机位置
            UpdateCameraPosition();

            Debug.Log($"角色模型 {characterModel.name} 已设置完成");
        }
    }

    void SetCharacterVisibility(bool hide)
    {
        if (characterModel != null)
        {
            Renderer[] renderers = characterModel.GetComponentsInChildren<Renderer>();
            foreach (Renderer renderer in renderers)
            {
                renderer.enabled = !hide;
            }
        }
    }

    void HandleViewToggle()
    {
        if (Input.GetKeyDown(toggleViewKey))
        {
            isThirdPerson = !isThirdPerson;
            UpdateCameraPosition();
            SetCharacterVisibility(!isThirdPerson && hideModelInFirstPerson);
            Debug.Log($"切换到 {(isThirdPerson ? "第三人称" : "第一人称")} 视角");
        }
    }

    void UpdateCameraPosition()
    {
        if (playerCamera == null) return;

        if (isThirdPerson)
        {
            playerCamera.transform.localPosition = thirdPersonOffset;
        }
        else
        {
            playerCamera.transform.localPosition = new Vector3(0, 1.8f, 0);
        }
    }
}
