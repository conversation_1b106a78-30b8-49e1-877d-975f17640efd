{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 30272, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 30272, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 30272, "tid": 577, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 30272, "tid": 577, "ts": 1754129637907434, "dur": 891, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 30272, "tid": 577, "ts": 1754129637911543, "dur": 594, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 30272, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 30272, "tid": 1, "ts": 1754129637191985, "dur": 3356, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754129637195343, "dur": 279663, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754129637475017, "dur": 23077, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 30272, "tid": 577, "ts": 1754129637912151, "dur": 27, "ph": "X", "name": "", "args": {}}, {"pid": 30272, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637190760, "dur": 8587, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637199348, "dur": 695710, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637200014, "dur": 2109, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637202127, "dur": 1168, "ph": "X", "name": "ProcessMessages 8670", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203297, "dur": 449, "ph": "X", "name": "ReadAsync 8670", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203749, "dur": 11, "ph": "X", "name": "ProcessMessages 20570", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203761, "dur": 64, "ph": "X", "name": "ReadAsync 20570", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203830, "dur": 2, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203833, "dur": 41, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203876, "dur": 29, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203908, "dur": 34, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203944, "dur": 28, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637203976, "dur": 29, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204008, "dur": 38, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204048, "dur": 29, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204082, "dur": 45, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204130, "dur": 32, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204165, "dur": 39, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204206, "dur": 38, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204247, "dur": 30, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204279, "dur": 119, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204400, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204404, "dur": 45, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204452, "dur": 31, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204485, "dur": 29, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204516, "dur": 39, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204558, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204582, "dur": 48, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204633, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204670, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204687, "dur": 19, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204708, "dur": 32, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204742, "dur": 34, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204778, "dur": 26, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204809, "dur": 18, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204829, "dur": 17, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204848, "dur": 16, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204867, "dur": 16, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204884, "dur": 29, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204915, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204937, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204960, "dur": 14, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637204975, "dur": 44, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205021, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205044, "dur": 16, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205062, "dur": 18, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205082, "dur": 17, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205100, "dur": 16, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205117, "dur": 16, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205134, "dur": 19, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205155, "dur": 18, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205175, "dur": 15, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205193, "dur": 111, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205306, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205324, "dur": 17, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205343, "dur": 16, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205360, "dur": 51, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205413, "dur": 25, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205440, "dur": 12, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205453, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205494, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205516, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205518, "dur": 25, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205544, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205564, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205585, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205606, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205628, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205629, "dur": 32, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205663, "dur": 18, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205682, "dur": 14, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205697, "dur": 17, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205716, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205733, "dur": 18, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205753, "dur": 76, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205834, "dur": 3, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205839, "dur": 33, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205874, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205879, "dur": 33, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205915, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205918, "dur": 32, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205952, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637205972, "dur": 27, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206001, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206031, "dur": 47, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206079, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206105, "dur": 23, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206129, "dur": 55, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206186, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206187, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206220, "dur": 29, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206251, "dur": 72, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206325, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206348, "dur": 13, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206363, "dur": 31, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206395, "dur": 35, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206431, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206445, "dur": 17, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206464, "dur": 16, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206482, "dur": 21, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206506, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206508, "dur": 20, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206529, "dur": 14, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206545, "dur": 19, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206566, "dur": 14, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206581, "dur": 33, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206616, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206639, "dur": 15, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206655, "dur": 30, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206686, "dur": 20, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206706, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206708, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206731, "dur": 53, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206786, "dur": 38, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206825, "dur": 14, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206842, "dur": 13, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206858, "dur": 51, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206910, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206942, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206968, "dur": 24, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637206994, "dur": 28, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207024, "dur": 16, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207041, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207065, "dur": 31, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207098, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207101, "dur": 28, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207131, "dur": 15, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207147, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207164, "dur": 18, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207184, "dur": 14, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207201, "dur": 17, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207219, "dur": 43, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207266, "dur": 16, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207285, "dur": 33, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207322, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207324, "dur": 49, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207376, "dur": 42, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207421, "dur": 42, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207464, "dur": 47, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207513, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207515, "dur": 45, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207562, "dur": 27, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207592, "dur": 31, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207625, "dur": 44, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207673, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207701, "dur": 16, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207720, "dur": 21, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207742, "dur": 20, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207764, "dur": 19, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207784, "dur": 33, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207820, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207844, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207870, "dur": 13, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207885, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207908, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207932, "dur": 28, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637207963, "dur": 106, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208074, "dur": 2, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208077, "dur": 42, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208126, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208149, "dur": 57, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208210, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208212, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208232, "dur": 6, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208240, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208261, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208265, "dur": 34, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208303, "dur": 15, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208321, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208343, "dur": 16, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208362, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208366, "dur": 26, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208396, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208398, "dur": 28, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208430, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208433, "dur": 36, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208472, "dur": 22, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208496, "dur": 21, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208521, "dur": 13, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208536, "dur": 16, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208554, "dur": 17, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208573, "dur": 14, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208589, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208612, "dur": 18, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208633, "dur": 33, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208667, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208685, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208706, "dur": 12, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208720, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208739, "dur": 12, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208752, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208769, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208784, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208800, "dur": 14, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208816, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208833, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208855, "dur": 14, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208871, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208887, "dur": 13, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208903, "dur": 14, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208918, "dur": 14, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208933, "dur": 14, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208949, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208972, "dur": 21, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637208995, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209010, "dur": 149, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209161, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209183, "dur": 14, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209200, "dur": 14, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209215, "dur": 15, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209232, "dur": 13, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209247, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209265, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209285, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209313, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209317, "dur": 24, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209342, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209346, "dur": 27, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209375, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209377, "dur": 10, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209388, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209391, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209413, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209431, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209448, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209464, "dur": 14, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209479, "dur": 13, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209494, "dur": 23, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209519, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209541, "dur": 14, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209557, "dur": 33, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209591, "dur": 15, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209607, "dur": 13, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209621, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209637, "dur": 13, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209651, "dur": 18, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209672, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209687, "dur": 14, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209704, "dur": 12, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209717, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209733, "dur": 15, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209748, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209750, "dur": 9, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209761, "dur": 25, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209787, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209807, "dur": 14, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209822, "dur": 16, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209840, "dur": 9, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209851, "dur": 16, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209868, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209886, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209904, "dur": 29, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209937, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637209940, "dur": 76, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210019, "dur": 3, "ph": "X", "name": "ProcessMessages 1193", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210022, "dur": 22, "ph": "X", "name": "ReadAsync 1193", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210047, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210065, "dur": 24, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210090, "dur": 11, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210103, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210123, "dur": 14, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210138, "dur": 16, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210155, "dur": 26, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210182, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210183, "dur": 18, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210202, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210218, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210236, "dur": 14, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210251, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210267, "dur": 28, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210298, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210301, "dur": 46, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210349, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210351, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210374, "dur": 15, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210390, "dur": 13, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210405, "dur": 31, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210440, "dur": 35, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210478, "dur": 26, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210505, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210507, "dur": 16, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210526, "dur": 26, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210554, "dur": 30, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210586, "dur": 24, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210613, "dur": 25, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210640, "dur": 48, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210689, "dur": 26, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210717, "dur": 14, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210732, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210768, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210793, "dur": 36, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210831, "dur": 29, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210863, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210881, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210918, "dur": 15, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210935, "dur": 15, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210951, "dur": 31, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637210985, "dur": 260039, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637471032, "dur": 4, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637471039, "dur": 396, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637471436, "dur": 12, "ph": "X", "name": "ProcessMessages 20529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637471449, "dur": 647, "ph": "X", "name": "ReadAsync 20529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472098, "dur": 1, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472100, "dur": 213, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472320, "dur": 5, "ph": "X", "name": "ProcessMessages 3588", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472325, "dur": 57, "ph": "X", "name": "ReadAsync 3588", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472383, "dur": 25, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472411, "dur": 79, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472493, "dur": 61, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472564, "dur": 2, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472568, "dur": 178, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472748, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472752, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472797, "dur": 40, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472841, "dur": 39, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472883, "dur": 37, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472922, "dur": 73, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637472998, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473038, "dur": 32, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473072, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473094, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473160, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473192, "dur": 11, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473204, "dur": 19, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473242, "dur": 27, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473272, "dur": 64, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473338, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473415, "dur": 36, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473453, "dur": 39, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473494, "dur": 9, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473504, "dur": 163, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473669, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473701, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473724, "dur": 17, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473743, "dur": 26, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473771, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473792, "dur": 20, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473814, "dur": 113, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473928, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473961, "dur": 20, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637473983, "dur": 26, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474014, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474037, "dur": 44, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474083, "dur": 14, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474098, "dur": 107, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474208, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474233, "dur": 23, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474257, "dur": 14, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474273, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474278, "dur": 26, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474304, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474307, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474330, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474402, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474441, "dur": 36, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474479, "dur": 19, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474500, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474521, "dur": 17, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474540, "dur": 135, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474678, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474720, "dur": 29, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474752, "dur": 19, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474773, "dur": 98, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474872, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474902, "dur": 40, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474944, "dur": 17, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474962, "dur": 23, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637474987, "dur": 17, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475007, "dur": 21, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475030, "dur": 93, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475125, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475156, "dur": 25, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475182, "dur": 16, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475199, "dur": 78, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475281, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475285, "dur": 33, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475320, "dur": 46, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475368, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475392, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475413, "dur": 26, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475441, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475462, "dur": 68, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475533, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475572, "dur": 14, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475588, "dur": 79, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475670, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475722, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475725, "dur": 48, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475775, "dur": 38, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475815, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475817, "dur": 18, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475837, "dur": 101, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475943, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637475986, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476014, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476066, "dur": 61, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476131, "dur": 8, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476140, "dur": 57, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476199, "dur": 49, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476251, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476253, "dur": 43, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476298, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476366, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476397, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476413, "dur": 25, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476441, "dur": 81, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476525, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476541, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476543, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476611, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476690, "dur": 4, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476695, "dur": 65, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476766, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476768, "dur": 53, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476836, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476899, "dur": 1, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476901, "dur": 33, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476937, "dur": 55, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637476995, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477044, "dur": 5, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477050, "dur": 25, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477075, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477078, "dur": 67, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477147, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477192, "dur": 27, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477226, "dur": 26, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477256, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477310, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477339, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477361, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477389, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477416, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477473, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477510, "dur": 42, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477555, "dur": 26, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477583, "dur": 12, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477597, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477655, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477680, "dur": 80, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477764, "dur": 44, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477810, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477815, "dur": 42, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477858, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477889, "dur": 52, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477943, "dur": 25, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477971, "dur": 16, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637477988, "dur": 43, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478036, "dur": 3, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478040, "dur": 100, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478143, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478189, "dur": 44, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478236, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478273, "dur": 41, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478317, "dur": 40, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478359, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478361, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478418, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478453, "dur": 39, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478494, "dur": 30, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478526, "dur": 19, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478547, "dur": 83, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478633, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478706, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478708, "dur": 43, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478759, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478764, "dur": 45, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478812, "dur": 2, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478816, "dur": 32, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478850, "dur": 27, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478879, "dur": 66, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478949, "dur": 40, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637478992, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479019, "dur": 155, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479178, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479216, "dur": 34, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479253, "dur": 52, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479307, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479311, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479360, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479365, "dur": 50, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479417, "dur": 32, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479452, "dur": 40, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479494, "dur": 47, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479544, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479545, "dur": 33, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479580, "dur": 34, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479616, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479673, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479675, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479711, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479712, "dur": 59, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479773, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479799, "dur": 11, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479811, "dur": 34, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479850, "dur": 50, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479902, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479907, "dur": 35, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479943, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479946, "dur": 33, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479980, "dur": 14, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637479998, "dur": 20, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480021, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480044, "dur": 54, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480099, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480123, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480146, "dur": 21, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480169, "dur": 27, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480200, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480242, "dur": 70, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480313, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480315, "dur": 41, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480358, "dur": 23, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480383, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480406, "dur": 61, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480476, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480539, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480545, "dur": 128, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480676, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480678, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480762, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480765, "dur": 52, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480819, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637480821, "dur": 180, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481004, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481039, "dur": 19, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481060, "dur": 33, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481097, "dur": 34, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481133, "dur": 49, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481184, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481186, "dur": 24, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481213, "dur": 24, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481238, "dur": 26, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481266, "dur": 22, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481290, "dur": 14, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481306, "dur": 19, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481328, "dur": 12, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481342, "dur": 69, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481413, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481431, "dur": 157, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481590, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481634, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481637, "dur": 53, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481693, "dur": 3, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481697, "dur": 33, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481735, "dur": 40, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481777, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481779, "dur": 29, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481811, "dur": 29, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481842, "dur": 19, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481863, "dur": 29, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481894, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481916, "dur": 19, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481937, "dur": 22, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481960, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637481985, "dur": 71, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482058, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482085, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482086, "dur": 40, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482128, "dur": 14, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482144, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482146, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482190, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482193, "dur": 85, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482280, "dur": 2, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482283, "dur": 41, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482327, "dur": 36, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482365, "dur": 31, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482400, "dur": 41, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482447, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482490, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482493, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482531, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482535, "dur": 34, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482570, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482571, "dur": 34, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482609, "dur": 54, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482665, "dur": 36, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482705, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482760, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482810, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482812, "dur": 34, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482848, "dur": 28, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482877, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482878, "dur": 19, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482899, "dur": 23, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482924, "dur": 16, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482943, "dur": 15, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482960, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637482987, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483011, "dur": 133, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483147, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483187, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483189, "dur": 50, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483241, "dur": 3, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483246, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483268, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483305, "dur": 28, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483337, "dur": 30, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483369, "dur": 34, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483407, "dur": 23, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483432, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483463, "dur": 34, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483500, "dur": 97, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483604, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483644, "dur": 166, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483814, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483819, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637483861, "dur": 375, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484239, "dur": 82, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484325, "dur": 3, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484330, "dur": 22, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484355, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484387, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484390, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484447, "dur": 83, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484533, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484535, "dur": 107, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484645, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484652, "dur": 52, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484707, "dur": 4, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484712, "dur": 80, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484794, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484800, "dur": 75, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484880, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484886, "dur": 88, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484984, "dur": 9, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637484994, "dur": 64, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485060, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485062, "dur": 60, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485129, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485132, "dur": 50, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485187, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485189, "dur": 53, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485246, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485247, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485290, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485354, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485419, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485421, "dur": 43, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485465, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485468, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485527, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485532, "dur": 83, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485616, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485619, "dur": 77, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485703, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485707, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485762, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485764, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485826, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485831, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485888, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485890, "dur": 59, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485954, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637485957, "dur": 58, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486018, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486020, "dur": 58, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486081, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486085, "dur": 42, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486129, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486130, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486160, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486186, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486218, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486249, "dur": 43, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486296, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486302, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486374, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486379, "dur": 58, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486440, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486442, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486492, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486536, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486539, "dur": 127, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486671, "dur": 85, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486759, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486761, "dur": 38, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486802, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486831, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486864, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486893, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486923, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486963, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637486965, "dur": 6808, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637493778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637493779, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637493807, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637493809, "dur": 1813, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637495627, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637495629, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637495663, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637495763, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637495824, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637495830, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637495873, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637495874, "dur": 1302, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637497182, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637497230, "dur": 1535, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637498768, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637498771, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637498818, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637498821, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637498866, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637498891, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637498937, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637498955, "dur": 279, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499237, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499252, "dur": 304, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499560, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499598, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499621, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499671, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499708, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499710, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499741, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499783, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499808, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499827, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499859, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499879, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499917, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637499940, "dur": 216, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500159, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500201, "dur": 363, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500567, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500603, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500606, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500699, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500717, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500756, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500779, "dur": 61, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500848, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500889, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500890, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500914, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500931, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500950, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637500990, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501009, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501043, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501073, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501099, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501134, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501137, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501195, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501197, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501242, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501247, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501310, "dur": 122, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501439, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501484, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501537, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501580, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501584, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501835, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637501871, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502025, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502051, "dur": 128, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502182, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502224, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502262, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502265, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502318, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502355, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502384, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502498, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502518, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502549, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502579, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502602, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502628, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502660, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502665, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502698, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502699, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502756, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502791, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502823, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502857, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502888, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502946, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502981, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637502983, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503228, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503280, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503284, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503319, "dur": 241, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503563, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503567, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503600, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503712, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503748, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503809, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503811, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637503847, "dur": 279, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504128, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504159, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504224, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504269, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504271, "dur": 72, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504345, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504362, "dur": 400, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504765, "dur": 34, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504803, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504846, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504848, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637504979, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505016, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505018, "dur": 426, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505447, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505449, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505486, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505490, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505532, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505599, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505602, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505662, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505694, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505732, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505757, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505808, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637505824, "dur": 434, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506261, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506263, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506314, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506377, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506507, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506509, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506558, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506595, "dur": 306, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506902, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506929, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637506931, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637507019, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637507041, "dur": 517, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637507561, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637507582, "dur": 59739, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637567339, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637567350, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637567450, "dur": 2975, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637570428, "dur": 5210, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637575644, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637575664, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637575889, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637575922, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637575960, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637575961, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637575979, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576076, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576097, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576237, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576286, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576363, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576437, "dur": 148, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576589, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576606, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576609, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576714, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576740, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576746, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576840, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576874, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576941, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637576969, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577058, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577062, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577078, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577080, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577281, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577313, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577316, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577559, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577599, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577936, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637577959, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637578006, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637578037, "dur": 793, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637578832, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637578868, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637578964, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579018, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579057, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579079, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579102, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579151, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579174, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579216, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579239, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579299, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579340, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579343, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579481, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579581, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579585, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579692, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579695, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579760, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579803, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579864, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579900, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579932, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637579993, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580031, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580065, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580170, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580226, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580249, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580438, "dur": 277, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580720, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580775, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580778, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580831, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580833, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580861, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580897, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580940, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637580955, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581021, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581064, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581120, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581165, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581206, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581231, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581261, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581323, "dur": 510, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581838, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637581889, "dur": 2501, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637584400, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637584474, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637584477, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637584511, "dur": 364, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637584883, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637584934, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585063, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585115, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585155, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585200, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585238, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585277, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585302, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585333, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585355, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585392, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585425, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585487, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585490, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585513, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585570, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585572, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585638, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585673, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585710, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585714, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585783, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585814, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585816, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585850, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637585896, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637586092, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637586128, "dur": 86, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637586217, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637586266, "dur": 15, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637586282, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637586336, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637586338, "dur": 98404, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637684747, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637684750, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637684810, "dur": 21, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637684831, "dur": 39659, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637724497, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637724500, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637724531, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637724534, "dur": 87886, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637812430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637812433, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637812489, "dur": 24, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637812514, "dur": 8157, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637820678, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637820682, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637820709, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637820712, "dur": 2296, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637823012, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637823015, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637823067, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637823097, "dur": 55598, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637878711, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637878715, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637878766, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637878772, "dur": 627, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637879404, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637879408, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637879513, "dur": 47, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637879562, "dur": 1029, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637880597, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637880600, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637880642, "dur": 1213, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129637881858, "dur": 13127, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 30272, "tid": 577, "ts": 1754129637912190, "dur": 3216, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129637189035, "dur": 309112, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129637498152, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129637498157, "dur": 995, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 30272, "tid": 577, "ts": 1754129637915408, "dur": 17, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 30272, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129637178066, "dur": 718698, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129637180557, "dur": 4889, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129637896902, "dur": 5855, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129637900480, "dur": 42, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129637902876, "dur": 30, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 30272, "tid": 577, "ts": 1754129637915428, "dur": 15, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754129637198459, "dur": 50, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637198535, "dur": 1329, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637199875, "dur": 647, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637200666, "dur": 657, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637201759, "dur": 205, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_099DA8B5C5E5CF06.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129637202850, "dur": 1266, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F073B613EC8B985D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129637204157, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129637204233, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129637204412, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129637204664, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754129637204811, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129637205133, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129637206177, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A6E1C780C67641AE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129637206478, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129637207775, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754129637210345, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129637212603, "dur": 259260, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754129637472577, "dur": 174, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754129637473158, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129637473732, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754129637475273, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754129637475587, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129637476829, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129637478054, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754129637478259, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754129637479026, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129637479241, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754129637480567, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754129637481078, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129637481501, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754129637482041, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754129637482179, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129637482567, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754129637201351, "dur": 282635, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637484005, "dur": 395880, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637879887, "dur": 569, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637880480, "dur": 126, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637880866, "dur": 56, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637880956, "dur": 1931, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754129637201298, "dur": 282818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637484131, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129637484319, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637484423, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17CAD97A34817613.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129637484516, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129637484650, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_192890A86BC12540.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129637484731, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129637484932, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129637485033, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129637485282, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129637485595, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129637485744, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129637485971, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637486033, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129637486366, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754129637486714, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129637486980, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129637487156, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637487225, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637488887, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637489759, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637490449, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637491088, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637492503, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637493374, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637494029, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637494988, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637496178, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637496887, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637497331, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637498459, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637499220, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637499746, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637501120, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637501567, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637501762, "dur": 1247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637503009, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637503663, "dur": 1323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637504987, "dur": 66342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637571331, "dur": 7168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754129637578500, "dur": 836, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637579363, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637579527, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637579625, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637579703, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637579868, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637580181, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637580269, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637580422, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637581125, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637581478, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637581636, "dur": 3130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637584831, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637585384, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129637585826, "dur": 294078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637201274, "dur": 282818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637484108, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637484261, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C0425020FB5520E6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637484323, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EA8F9A3BC19611D7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637484413, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637484610, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637484702, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637484956, "dur": 694, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637485663, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754129637485860, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754129637486041, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754129637486500, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754129637486724, "dur": 415, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754129637487140, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cloud.gltfast@6.13.1\\Runtime\\Scripts\\Schema\\FakeSchema\\MaterialExtension.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754129637487140, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637488624, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637489134, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637490214, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637490914, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637492175, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637493129, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637494044, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637494633, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637496109, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637496697, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637497971, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637498307, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637498394, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637498542, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637499202, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637499696, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637500296, "dur": 2620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754129637503048, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637503247, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754129637504375, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637504619, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129637504716, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754129637505184, "dur": 69472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637574659, "dur": 7408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754129637582068, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637582228, "dur": 3040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637585277, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637585497, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129637586229, "dur": 293729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637201217, "dur": 282828, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637484077, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637484525, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637484671, "dur": 873, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129637485553, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129637485696, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637485756, "dur": 526, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129637486329, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754129637486566, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129637486660, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129637486756, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129637486842, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9630950048426576611.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129637487102, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9630950048426576611.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129637487300, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637488790, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637489808, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637490684, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637491846, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637493010, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637493523, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637494357, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637495762, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637496596, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637497459, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637498408, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637498462, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637499206, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637499704, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129637500170, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754129637501364, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637501441, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129637501620, "dur": 1552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754129637503245, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637503688, "dur": 1170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637504858, "dur": 66421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637571293, "dur": 5729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754129637577084, "dur": 8102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754129637585187, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637585275, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637585492, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129637586200, "dur": 293786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637201491, "dur": 282739, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637484249, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129637484340, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_27474F036FBEA425.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129637484648, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129637484723, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129637484826, "dur": 620, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129637485545, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129637485775, "dur": 715, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129637486493, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129637486625, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129637486847, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10131660672807692368.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129637486972, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129637487178, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637487240, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637488419, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637489068, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637490021, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637490625, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637491158, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637492629, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637493668, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637494310, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637495738, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637496763, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637497380, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637498590, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637499218, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637499755, "dur": 1340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637501141, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637501494, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637501546, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129637501855, "dur": 1145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637503001, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637503650, "dur": 1165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637504817, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754129637505370, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754129637505711, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637505812, "dur": 65557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637571387, "dur": 7600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754129637578988, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637579205, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637579336, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637579436, "dur": 799, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637580290, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637580445, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637580528, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637580646, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637580798, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754129637581091, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754129637581295, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637581466, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637581576, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754129637581640, "dur": 3613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637585284, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637585604, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637585772, "dur": 235017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129637820833, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754129637820793, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754129637821020, "dur": 2353, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754129637823383, "dur": 56636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637201205, "dur": 282814, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637484074, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637484234, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129637484359, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129637484657, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129637484929, "dur": 994, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129637485932, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129637486035, "dur": 748, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129637486784, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17923285327036797829.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129637486875, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129637487043, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129637487148, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637488125, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637488898, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637489926, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637491195, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Utility\\Logic\\BranchNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754129637490873, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637492803, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637493534, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637494329, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637494820, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637495895, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637496586, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637497290, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637498256, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637498555, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637499243, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637499735, "dur": 1365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637501100, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637501296, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637501550, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637501668, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129637501927, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129637503161, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129637503318, "dur": 1656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129637505032, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129637505183, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129637505994, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129637506108, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129637506636, "dur": 64715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637571365, "dur": 6649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754129637578015, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129637578313, "dur": 7826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754129637586239, "dur": 293712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637201266, "dur": 282809, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637484087, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129637484391, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129637484672, "dur": 651, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_95DEE68278F6B037.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129637485327, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_440D2CBC9242B582.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129637485404, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_440D2CBC9242B582.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129637485766, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129637485866, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129637486208, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129637486365, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754129637486488, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129637486594, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129637486833, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129637486934, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129637487004, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129637487137, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637488019, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637488951, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637489949, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637491249, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Manipulators\\WindowDraggable.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754129637490594, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637491816, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637493037, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637493875, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637496537, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Unity\\LudiqScriptableObject.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754129637494642, "dur": 2572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637497214, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637497310, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637498266, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637498488, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637499204, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637499691, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129637500048, "dur": 1367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754129637501541, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129637501798, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754129637502836, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637502973, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129637503151, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754129637503642, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1754129637504288, "dur": 148, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637504751, "dur": 62931, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1754129637571279, "dur": 5831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Newtonsoft.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754129637577111, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637577215, "dur": 8920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754129637586139, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637586222, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129637586433, "dur": 293565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637201343, "dur": 282811, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637484165, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_43A3C34EEA8A405D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129637484325, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_3728351D5FA9AF42.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129637484457, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129637484661, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129637484873, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637485283, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637485389, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637485555, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637485758, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754129637485988, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637486404, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637486815, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637486892, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637487050, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129637487152, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637488339, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637489481, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637490464, "dur": 1648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637492112, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637493315, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637494069, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637494746, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637495834, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637496489, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637497103, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637498356, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637498564, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637499219, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637499737, "dur": 1357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637501132, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637501507, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637501570, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637501735, "dur": 1251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637502987, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637503645, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637503962, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129637504173, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637504834, "dur": 66542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637571382, "dur": 4488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754129637575873, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637575994, "dur": 8636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754129637584632, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637584765, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637584839, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637585494, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637585558, "dur": 897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129637586479, "dur": 293460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637202004, "dur": 282450, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637484460, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129637484682, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129637485052, "dur": 445, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754129637485586, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129637485763, "dur": 372, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754129637486330, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129637486628, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754129637486863, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129637486978, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129637487045, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129637487139, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637488173, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637488808, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637489370, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637490773, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637491859, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637493246, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector2\\Vector2Multiply.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754129637492863, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637494110, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637495016, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637496037, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637496132, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637496725, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637497138, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637497213, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637497372, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637498093, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637498563, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637499227, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637499742, "dur": 1355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637501106, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637501211, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637501502, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637501582, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637501723, "dur": 1262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637502985, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637503648, "dur": 975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637504626, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129637504941, "dur": 968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754129637505990, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129637506110, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754129637506757, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129637506841, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129637506954, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754129637507297, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129637507400, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754129637507963, "dur": 177143, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754129637686643, "dur": 33753, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754129637686635, "dur": 36499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754129637724635, "dur": 173, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129637724863, "dur": 87912, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754129637820788, "dur": 58186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754129637820780, "dur": 58196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754129637879008, "dur": 759, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754129637202101, "dur": 282386, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637484493, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129637484644, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129637484713, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129637484810, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129637485072, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637485291, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637485766, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637485852, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637485997, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637486194, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637486310, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637486474, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637486860, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16800515999216397072.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637487073, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129637487211, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637488329, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637488913, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637489920, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637490631, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637492019, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637492986, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637493760, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637494940, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637496289, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637496871, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637498207, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637498378, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637498534, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637499195, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637499694, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129637500096, "dur": 1335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754129637501432, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637501670, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129637501864, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754129637502660, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637502888, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637503022, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637503671, "dur": 1285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637504956, "dur": 66981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129637571940, "dur": 5429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754129637577423, "dur": 8443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754129637585957, "dur": 293957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637201434, "dur": 282763, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637484321, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_78AB31D22221CD7D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129637484468, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637484677, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_C5B475C4637F1A14.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129637484943, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754129637485194, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129637485509, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637485561, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754129637485750, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129637485926, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129637486212, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129637486397, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754129637486553, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129637486722, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129637486809, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129637486954, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129637487169, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637488538, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637489581, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637491100, "dur": 680, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SamplerStateNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754129637490624, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637492129, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637492976, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637493661, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637495197, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637496681, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637497034, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637497560, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637498573, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637499239, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637499732, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129637500089, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129637500759, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637501255, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129637501591, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129637502375, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637502577, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637503033, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637503673, "dur": 1226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637504900, "dur": 66394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637571307, "dur": 9628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754129637580937, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637580992, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754129637581180, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637581286, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637581388, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1754129637581459, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637581625, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637582252, "dur": 3095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637585387, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129637585972, "dur": 293949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637201528, "dur": 282726, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637484271, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_099DA8B5C5E5CF06.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129637484325, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637484408, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_F5A419B9F1F8246D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129637484651, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129637484842, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754129637485128, "dur": 589, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754129637485760, "dur": 959, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754129637486721, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129637486823, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3676569134375922281.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129637486988, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129637487121, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637487834, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637488789, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637490094, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637490592, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637491723, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637492619, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637493755, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637494628, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637495398, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637496542, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637497235, "dur": 84, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637497320, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637498425, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637499194, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637499550, "dur": 1763, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637501313, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637501510, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637501564, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637501750, "dur": 1241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637502991, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637503677, "dur": 1214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637504892, "dur": 66454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637571350, "dur": 5198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Export.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754129637576594, "dur": 8943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754129637585599, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637585674, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129637586650, "dur": 293328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637201578, "dur": 282695, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637484339, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637484474, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637484667, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129637485125, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129637485242, "dur": 374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129637485618, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129637486036, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754129637486371, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754129637486627, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754129637486912, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129637486999, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129637487123, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637488309, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637489010, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637490262, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637491737, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637492832, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637493488, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637494196, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637494816, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637495895, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637496722, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637497730, "dur": 711, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754129637497120, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637498465, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637499214, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637499749, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129637500074, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754129637501136, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637501542, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129637501806, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754129637502730, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637503002, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637503673, "dur": 1274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637504947, "dur": 66407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637571369, "dur": 5176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754129637576546, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637576739, "dur": 8914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754129637585654, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129637585837, "dur": 294042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637201622, "dur": 282668, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637484330, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637484454, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637484605, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637484665, "dur": 1063, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129637485767, "dur": 405, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754129637486174, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129637486343, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754129637486668, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129637486764, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129637486864, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129637487179, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637487257, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637488556, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637489423, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637490158, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637490623, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637491942, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637493034, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637493969, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637494818, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637495849, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637497467, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637498543, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637499201, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637499698, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129637499940, "dur": 1171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754129637501112, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637501328, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637501586, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637501702, "dur": 1271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637502982, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129637503169, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754129637503681, "dur": 1235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637504916, "dur": 66473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637571392, "dur": 5167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dots.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754129637576598, "dur": 8855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754129637585454, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637585695, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129637586633, "dur": 293267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637201674, "dur": 282635, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637484322, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_7359D068AFC0FC5C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637484612, "dur": 1420, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4641433ED6B9F892.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637486070, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637486276, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637486330, "dur": 9500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754129637495831, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637495993, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637496236, "dur": 1270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637497543, "dur": 1554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754129637499232, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637499322, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754129637499727, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637499900, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637499969, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754129637501165, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637501252, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129637501588, "dur": 853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754129637502442, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637502600, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637503008, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637503679, "dur": 1191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637504871, "dur": 66438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637571314, "dur": 6412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Documentation.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754129637577728, "dur": 638, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129637578381, "dur": 8182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754129637586656, "dur": 293286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637201712, "dur": 282614, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637484338, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129637484536, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129637484603, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129637484674, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129637484968, "dur": 424, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129637485395, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129637485591, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129637485667, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129637485934, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129637486374, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754129637486671, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129637486768, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129637486867, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129637487171, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637487253, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637488537, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637489110, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637489821, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637490773, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637491856, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637492882, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637493542, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637494192, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637494942, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637496149, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637497208, "dur": 609, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\PlasticSplitterGUILayout.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754129637496746, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637497857, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637497922, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637498119, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637498554, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637499249, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637499717, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129637500104, "dur": 2152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754129637502257, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637502416, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129637502680, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754129637503216, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637503690, "dur": 1192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637504882, "dur": 66400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637571296, "dur": 5449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754129637576747, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637576965, "dur": 8708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754129637585735, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129637585798, "dur": 294089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637201775, "dur": 282568, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637484359, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129637484583, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129637484683, "dur": 531, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129637485246, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754129637485557, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754129637485761, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129637485897, "dur": 421, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129637486358, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754129637486448, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637486540, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129637486629, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129637486991, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129637487102, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637487226, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637488346, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637489111, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637490205, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637491234, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637492485, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637493266, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637493957, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637494647, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637495954, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637496643, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637497603, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637498521, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637499207, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637499701, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129637500300, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754129637501307, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637501432, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637501572, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637501739, "dur": 1264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637503003, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637503655, "dur": 1157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637504813, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754129637505228, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637505350, "dur": 65987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637571341, "dur": 6517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754129637577859, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129637577931, "dur": 8474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754129637586476, "dur": 293490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637201811, "dur": 282558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637484408, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637484477, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E69732DB14D8F64.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129637484588, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637484681, "dur": 326, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_96EF797E350252A7.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129637485114, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129637485264, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1754129637485404, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_B79360A6DA5C7A31.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129637485529, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129637485661, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129637485760, "dur": 519, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1754129637486295, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1754129637486487, "dur": 643, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129637487131, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637488209, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637488952, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637489857, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637490523, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637491737, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637492620, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637493182, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637493938, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637494753, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637495826, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637496753, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637497267, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637498617, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637499224, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637499721, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129637500129, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637500192, "dur": 1128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129637501321, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637501511, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637501596, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637501688, "dur": 1288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637502977, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129637503154, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637503698, "dur": 1149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637504847, "dur": 66453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637571346, "dur": 5019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754129637576366, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637576460, "dur": 8620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754129637585082, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637585317, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637585693, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129637586618, "dur": 293342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637201851, "dur": 282531, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637484394, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129637484459, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637484674, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129637484965, "dur": 717, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129637485694, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637485762, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1754129637485923, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637485975, "dur": 556, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1754129637486533, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129637486622, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129637486990, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129637487095, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637488076, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637489583, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\AddDelete\\IAddDeleteItemMode.cs"}}, {"pid": 12345, "tid": 18, "ts": 1754129637488845, "dur": 1674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637490520, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637491225, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637492618, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637493444, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637494214, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637494867, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637496008, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 18, "ts": 1754129637496089, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637496159, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637496939, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637497972, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637498416, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637499195, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637499699, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129637500092, "dur": 1366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1754129637501458, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637501609, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637501678, "dur": 1300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637502978, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637503052, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129637503222, "dur": 1257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1754129637504517, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637504584, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637504820, "dur": 66468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637571293, "dur": 5254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754129637576548, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637576786, "dur": 9215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754129637586004, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129637586194, "dur": 293739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637201884, "dur": 282513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637484410, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129637484613, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9AD432D09FAC516B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129637484831, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129637485124, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1754129637485434, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129637485773, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129637486296, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1754129637486481, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637486618, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1754129637486930, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129637487109, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637488168, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637488791, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637489388, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637490490, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637491179, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Math\\Basic\\SubtractNode.cs"}}, {"pid": 12345, "tid": 19, "ts": 1754129637491095, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637492334, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637492980, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637493568, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637494210, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637494862, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637496184, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637496781, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637497417, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637498247, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637498465, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637499207, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637499702, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129637500078, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129637501289, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129637501559, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129637502642, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637502747, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637503012, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637503668, "dur": 1327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637504995, "dur": 66389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637571387, "dur": 5838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754129637577226, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637577325, "dur": 9193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754129637586519, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129637586652, "dur": 293240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637201922, "dur": 282489, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637484455, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637484645, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A5A0099F491A8369.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129637484746, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129637484864, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129637484941, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129637485242, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1754129637485542, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1754129637485901, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1754129637486036, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1754129637486451, "dur": 256, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129637486727, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1754129637487068, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129637487211, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637488214, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637489081, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637491166, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Importers\\ShaderSubGraphImporterEditor.cs"}}, {"pid": 12345, "tid": 20, "ts": 1754129637490311, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637491818, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637492384, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637493083, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637494074, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637494811, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637496116, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637496606, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637497736, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\SpriteStateDrawer.cs"}}, {"pid": 12345, "tid": 20, "ts": 1754129637497095, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637498330, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637498514, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637499222, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637499745, "dur": 1351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637501149, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637501555, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637501665, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129637501859, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637502392, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129637503053, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129637503233, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129637503954, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129637504075, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129637504817, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129637505346, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129637505876, "dur": 65485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637571365, "dur": 4879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754129637576245, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637576342, "dur": 8747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754129637585090, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637585407, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129637585993, "dur": 293933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637201972, "dur": 282462, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637484443, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129637484616, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129637484868, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129637485111, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129637485194, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129637485348, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A6E1C780C67641AE.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129637485419, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A6E1C780C67641AE.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129637485531, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1754129637485597, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129637485778, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1754129637486055, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129637486283, "dur": 7770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754129637494054, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637494193, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637494949, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637495840, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637497013, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637498340, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637498551, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637499209, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637499731, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129637499913, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754129637500558, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637501110, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637501490, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637501544, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129637501778, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637502191, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637503016, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637503677, "dur": 1230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637504907, "dur": 66407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637571317, "dur": 4841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754129637576159, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637576262, "dur": 9006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754129637585270, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637585375, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129637585774, "dur": 294109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637201332, "dur": 282803, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637484146, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41F9674DB235B210.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129637484229, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41F9674DB235B210.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129637484338, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129637484423, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637484505, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129637484686, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637484941, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129637485163, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129637485450, "dur": 449, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129637485929, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1754129637486232, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129637486434, "dur": 381, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129637486816, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129637487097, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637488372, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637489137, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637490193, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637491053, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637492300, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637493111, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637493848, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637494574, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637495341, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637496601, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637497497, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637498595, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637499226, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637499730, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129637499979, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754129637500872, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637500959, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637501101, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637501316, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637501511, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637501583, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637501711, "dur": 1303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637503015, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637503665, "dur": 1298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637504964, "dur": 66415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637571381, "dur": 6124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754129637577506, "dur": 793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129637578309, "dur": 8244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754129637586644, "dur": 293311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637202046, "dur": 282425, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637484518, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637484670, "dur": 600, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129637485572, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1754129637485942, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129637486370, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129637486484, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129637486559, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129637486896, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129637487197, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637488946, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637490216, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637491016, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637492461, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637493450, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637494107, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637494812, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637496176, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637496844, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637497369, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637498472, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637499215, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637499710, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129637500164, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129637501130, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637501489, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637501549, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637501669, "dur": 1314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637502984, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637503651, "dur": 1166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637504818, "dur": 1178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637506037, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129637506188, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129637506770, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129637506852, "dur": 64521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637571388, "dur": 6134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754129637577523, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637577651, "dur": 7762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754129637585414, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637585521, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129637586224, "dur": 293759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637201390, "dur": 282786, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637484193, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129637484322, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637484387, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129637484486, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129637484677, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129637485282, "dur": 399, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1754129637485718, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1754129637485868, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129637486029, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1754129637486274, "dur": 411, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1754129637486736, "dur": 380, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1754129637487117, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637488105, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637488999, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637490107, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637491284, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\BuiltInUnlitGUI.cs"}}, {"pid": 12345, "tid": 24, "ts": 1754129637490428, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637492158, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637493244, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637493819, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637494576, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637495590, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637496871, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637497460, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637498560, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637499235, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637499733, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129637500162, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637500238, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637501113, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637501488, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637501547, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637501668, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637502604, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637503011, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637503659, "dur": 1313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637504973, "dur": 66348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637571324, "dur": 5790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754129637577115, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637577237, "dur": 8627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754129637585866, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637585952, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129637586049, "dur": 293867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129637892504, "dur": 2092, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 30272, "tid": 577, "ts": 1754129637915903, "dur": 1978, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 30272, "tid": 577, "ts": 1754129637917915, "dur": 1723, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 30272, "tid": 577, "ts": 1754129637910683, "dur": 9493, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}