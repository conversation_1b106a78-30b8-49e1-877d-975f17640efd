{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 30272, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 30272, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 30272, "tid": 765, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 30272, "tid": 765, "ts": 1754130197698958, "dur": 794, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 30272, "tid": 765, "ts": 1754130197702822, "dur": 590, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 30272, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 30272, "tid": 1, "ts": 1754130197263959, "dur": 3198, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754130197267159, "dur": 21158, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754130197288325, "dur": 23581, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 30272, "tid": 765, "ts": 1754130197703417, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 30272, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197262790, "dur": 7938, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197270730, "dur": 417763, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197271381, "dur": 2747, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197274131, "dur": 977, "ph": "X", "name": "ProcessMessages 4746", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275110, "dur": 436, "ph": "X", "name": "ReadAsync 4746", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275557, "dur": 12, "ph": "X", "name": "ProcessMessages 20556", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275571, "dur": 52, "ph": "X", "name": "ReadAsync 20556", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275624, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275627, "dur": 21, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275653, "dur": 35, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275689, "dur": 3, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275693, "dur": 37, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275734, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275738, "dur": 41, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275783, "dur": 16, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275803, "dur": 24, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275829, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275851, "dur": 22, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275876, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275902, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275906, "dur": 20, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275928, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275955, "dur": 30, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197275989, "dur": 21, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276014, "dur": 19, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276035, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276037, "dur": 24, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276064, "dur": 20, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276088, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276113, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276117, "dur": 21, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276142, "dur": 24, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276168, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276170, "dur": 20, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276194, "dur": 19, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276216, "dur": 20, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276241, "dur": 20, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276264, "dur": 20, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276288, "dur": 26, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276319, "dur": 38, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276362, "dur": 15, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276381, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276406, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276410, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276435, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276456, "dur": 13, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276471, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276474, "dur": 11, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276488, "dur": 22, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276514, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276538, "dur": 23, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276564, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276567, "dur": 57, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276628, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276663, "dur": 22, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276688, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276690, "dur": 28, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276722, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276749, "dur": 38, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276790, "dur": 18, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276811, "dur": 51, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276867, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276900, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276903, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276929, "dur": 47, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276978, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197276981, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277016, "dur": 46, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277064, "dur": 1, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277066, "dur": 21, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277092, "dur": 28, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277122, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277124, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277152, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277179, "dur": 22, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277203, "dur": 29, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277237, "dur": 22, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277261, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277264, "dur": 17, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277282, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277285, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277302, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277304, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277331, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277334, "dur": 21, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277358, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277360, "dur": 18, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277380, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277382, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277412, "dur": 25, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277441, "dur": 22, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277465, "dur": 36, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277504, "dur": 2, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277507, "dur": 54, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277564, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277567, "dur": 22, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277592, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277619, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277642, "dur": 18, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277663, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277688, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277710, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277730, "dur": 17, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277749, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277770, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277794, "dur": 19, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277815, "dur": 18, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277836, "dur": 19, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277856, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277860, "dur": 17, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277879, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277881, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277899, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277902, "dur": 21, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277926, "dur": 20, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277950, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197277972, "dur": 28, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278003, "dur": 15, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278019, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278021, "dur": 13, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278037, "dur": 16, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278059, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278086, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278088, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278111, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278128, "dur": 37, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278168, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278191, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278211, "dur": 19, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278233, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278256, "dur": 15, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278273, "dur": 16, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278293, "dur": 14, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278311, "dur": 20, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278335, "dur": 26, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278363, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278385, "dur": 4, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278390, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278414, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278417, "dur": 17, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278440, "dur": 19, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278463, "dur": 25, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278493, "dur": 13, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278509, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278531, "dur": 19, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278556, "dur": 18, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278578, "dur": 16, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278599, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278617, "dur": 17, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278638, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278665, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278688, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278691, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278712, "dur": 16, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278730, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278731, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278751, "dur": 14, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278767, "dur": 13, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278783, "dur": 16, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278803, "dur": 13, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278820, "dur": 17, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278840, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278843, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278862, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278883, "dur": 16, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278902, "dur": 18, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278923, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278943, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278946, "dur": 18, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278965, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278967, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197278988, "dur": 19, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279012, "dur": 17, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279033, "dur": 16, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279053, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279077, "dur": 23, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279103, "dur": 21, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279128, "dur": 16, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279148, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279167, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279171, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279189, "dur": 119, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279314, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279336, "dur": 33, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279371, "dur": 2, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279375, "dur": 23, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279402, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279422, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279425, "dur": 14, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279443, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279446, "dur": 23, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279474, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279494, "dur": 23, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279521, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279546, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279571, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279588, "dur": 2, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279591, "dur": 16, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279611, "dur": 24, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279639, "dur": 17, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279660, "dur": 15, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279678, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279708, "dur": 25, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279737, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279742, "dur": 26, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279773, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279793, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279797, "dur": 118, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279920, "dur": 36, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279958, "dur": 2, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279961, "dur": 35, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197279998, "dur": 2, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280001, "dur": 24, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280029, "dur": 32, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280064, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280067, "dur": 40, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280111, "dur": 23, "ph": "X", "name": "ReadAsync 1205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280137, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280139, "dur": 33, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280175, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280178, "dur": 30, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280211, "dur": 1, "ph": "X", "name": "ProcessMessages 1111", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280212, "dur": 34, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280251, "dur": 31, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280285, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280308, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280313, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280337, "dur": 19, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280359, "dur": 32, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280395, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280398, "dur": 24, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280424, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280427, "dur": 18, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280447, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280452, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280472, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280490, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280492, "dur": 17, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280511, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280513, "dur": 19, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280536, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280538, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280557, "dur": 2, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280561, "dur": 11, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280575, "dur": 24, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280605, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280641, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280644, "dur": 20, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280669, "dur": 27, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280701, "dur": 23, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280731, "dur": 18, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280752, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280754, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280774, "dur": 18, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280797, "dur": 23, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280825, "dur": 22, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280849, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280850, "dur": 22, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280876, "dur": 3, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280884, "dur": 27, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280912, "dur": 4, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280918, "dur": 20, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280944, "dur": 40, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197280989, "dur": 29, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281024, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281040, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281042, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281061, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281066, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281092, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281113, "dur": 16, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281135, "dur": 12, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281151, "dur": 9, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281164, "dur": 18, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281184, "dur": 2, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281188, "dur": 20, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281209, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281211, "dur": 16, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281230, "dur": 19, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281250, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281252, "dur": 12, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281266, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281268, "dur": 16, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281286, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281288, "dur": 14, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281306, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281338, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281355, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281358, "dur": 15, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281375, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281378, "dur": 16, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281396, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281416, "dur": 16, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281439, "dur": 11, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281455, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281474, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281496, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281517, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281521, "dur": 14, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281539, "dur": 16, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281561, "dur": 17, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281581, "dur": 19, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281602, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281604, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281633, "dur": 16, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281652, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281675, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281695, "dur": 16, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281717, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281736, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281740, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281765, "dur": 27, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281798, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281801, "dur": 22, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281824, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281829, "dur": 10, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281852, "dur": 27, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281884, "dur": 22, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281910, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281913, "dur": 20, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281937, "dur": 17, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281962, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197281983, "dur": 12, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282001, "dur": 18, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282021, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282027, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282056, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282058, "dur": 18, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282079, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282081, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282099, "dur": 3, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282103, "dur": 17, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282123, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282124, "dur": 26, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282152, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282155, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282177, "dur": 18, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282202, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282234, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282264, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282287, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282290, "dur": 24, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282315, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282318, "dur": 12, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282332, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282334, "dur": 14, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282349, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282353, "dur": 16, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282377, "dur": 14, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282393, "dur": 17, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282416, "dur": 13, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282436, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282461, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282463, "dur": 20, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282488, "dur": 11, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282501, "dur": 2, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282504, "dur": 16, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282527, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282552, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282572, "dur": 20, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282594, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282596, "dur": 25, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282623, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282626, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282646, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282648, "dur": 13, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282663, "dur": 14, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282682, "dur": 68, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282760, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282775, "dur": 6, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282782, "dur": 19, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282803, "dur": 55, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282862, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282888, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282891, "dur": 19, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282919, "dur": 53, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282976, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197282978, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283022, "dur": 28, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283054, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283056, "dur": 66, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283123, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283147, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283170, "dur": 2, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283173, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283193, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283251, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283281, "dur": 21, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283306, "dur": 52, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283361, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283389, "dur": 27, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283418, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283421, "dur": 53, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283477, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283498, "dur": 19, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283518, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283520, "dur": 13, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283536, "dur": 50, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283590, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283613, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283615, "dur": 19, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283638, "dur": 53, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283694, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283719, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283725, "dur": 25, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283754, "dur": 44, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283806, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283838, "dur": 21, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283861, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283863, "dur": 67, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283931, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283937, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283961, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197283965, "dur": 38, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284008, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284011, "dur": 45, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284060, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284111, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284113, "dur": 24, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284139, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284140, "dur": 102, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284248, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284282, "dur": 26, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284312, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284336, "dur": 119, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284458, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284462, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284489, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284491, "dur": 21, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284515, "dur": 10, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284530, "dur": 63, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284596, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284600, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284628, "dur": 21, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284652, "dur": 16, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284670, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284692, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284696, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284717, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284721, "dur": 15, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284741, "dur": 91, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284835, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284877, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284882, "dur": 21, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284905, "dur": 2, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197284912, "dur": 195, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285120, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285189, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285191, "dur": 51, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285246, "dur": 43, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285294, "dur": 191, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285488, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285610, "dur": 3, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285616, "dur": 45, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285665, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285702, "dur": 41, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285748, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285751, "dur": 36, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285790, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285815, "dur": 65, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285883, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285886, "dur": 34, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285925, "dur": 64, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197285995, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286046, "dur": 35, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286086, "dur": 27, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286117, "dur": 60, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286188, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286230, "dur": 21, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286255, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286278, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286342, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286367, "dur": 20, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286388, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286390, "dur": 62, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286455, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286479, "dur": 18, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286499, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286518, "dur": 51, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286573, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286595, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286597, "dur": 19, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286622, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286627, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286654, "dur": 17, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286681, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286731, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286748, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286751, "dur": 35, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286789, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286791, "dur": 45, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286841, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286873, "dur": 29, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286903, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286905, "dur": 15, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286922, "dur": 2, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197286925, "dur": 83, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287010, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287066, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287068, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287098, "dur": 14, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287114, "dur": 89, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287205, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287232, "dur": 32, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287279, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287302, "dur": 53, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287357, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287382, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287404, "dur": 13, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287419, "dur": 66, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287488, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287508, "dur": 18, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287529, "dur": 62, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287592, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287615, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287617, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287640, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287641, "dur": 17, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287664, "dur": 51, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287720, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287740, "dur": 2, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287745, "dur": 17, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287764, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287767, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287789, "dur": 17, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287808, "dur": 15, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287825, "dur": 14, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287840, "dur": 13, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287857, "dur": 56, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287915, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287917, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287934, "dur": 14, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287950, "dur": 19, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287971, "dur": 22, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197287998, "dur": 28, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288029, "dur": 30, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288062, "dur": 23, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288087, "dur": 18, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288107, "dur": 72, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288184, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288222, "dur": 14, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288239, "dur": 66, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288310, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288331, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288349, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288369, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288390, "dur": 12, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288404, "dur": 16, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288422, "dur": 11, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288436, "dur": 16, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288453, "dur": 14, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288469, "dur": 56, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288527, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288550, "dur": 17, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288570, "dur": 19, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288591, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288611, "dur": 27, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288641, "dur": 15, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288658, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288682, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288697, "dur": 63, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288762, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288781, "dur": 19, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288802, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288819, "dur": 18, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288838, "dur": 17, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288858, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288879, "dur": 15, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288897, "dur": 14, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288912, "dur": 15, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288929, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197288993, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289011, "dur": 17, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289030, "dur": 17, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289048, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289072, "dur": 16, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289091, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289109, "dur": 13, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289124, "dur": 14, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289139, "dur": 16, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289157, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289209, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289237, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289260, "dur": 20, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289283, "dur": 33, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289317, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289335, "dur": 14, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289351, "dur": 14, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289367, "dur": 13, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289381, "dur": 13, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289396, "dur": 12, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289411, "dur": 18, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289430, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289446, "dur": 12, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289460, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289523, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289538, "dur": 15, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289554, "dur": 1, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289556, "dur": 19, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289577, "dur": 15, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289594, "dur": 17, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289614, "dur": 27, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289643, "dur": 3, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289649, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289670, "dur": 16, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289688, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289754, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289775, "dur": 75, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289852, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197289913, "dur": 310, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290226, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290331, "dur": 15, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290349, "dur": 38, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290388, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290390, "dur": 14, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290406, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290443, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290446, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290493, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290531, "dur": 21, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290555, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290595, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290599, "dur": 48, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290649, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290650, "dur": 40, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290692, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290694, "dur": 45, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290741, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290743, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290779, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290805, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290839, "dur": 23, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290864, "dur": 19, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290888, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290913, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290942, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290970, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290972, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197290992, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291011, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291033, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291057, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291085, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291124, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291131, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291192, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291195, "dur": 43, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291241, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291242, "dur": 34, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291280, "dur": 40, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291322, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291325, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291377, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291379, "dur": 42, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291425, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291426, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291470, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291490, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291528, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291530, "dur": 57, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291589, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291591, "dur": 63, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291660, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291661, "dur": 52, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291715, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291719, "dur": 37, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291758, "dur": 53, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291813, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291817, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291861, "dur": 22, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197291886, "dur": 139, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292028, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292029, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292069, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292072, "dur": 50, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292125, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292185, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292191, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292238, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292241, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197292276, "dur": 6518, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197298797, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197298831, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197298900, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197298921, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197299079, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197299114, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197299134, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197299155, "dur": 1336, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197300494, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197300514, "dur": 1303, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197301821, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197301861, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197301917, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197301949, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302014, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302041, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302387, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302410, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302449, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302654, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302657, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302704, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302706, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302747, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302795, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302850, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302889, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197302932, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303003, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303033, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303065, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303150, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303189, "dur": 513, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303707, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303712, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303758, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303760, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303809, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303811, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303845, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197303881, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304011, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304014, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304053, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304054, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304087, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304113, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304194, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304224, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304241, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304256, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304274, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304322, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304346, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304370, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304406, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304426, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304440, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304458, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304487, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304505, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304536, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304539, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304587, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304612, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304707, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304735, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304757, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304828, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304870, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304872, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304956, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197304995, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305042, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305044, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305095, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305126, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305127, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305237, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305255, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305256, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305279, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305303, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305314, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305346, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305401, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305440, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305442, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305466, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305488, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305510, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305548, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305574, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305599, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305627, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305647, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305704, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305726, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305767, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305804, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305938, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305941, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305991, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197305992, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306026, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306155, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306182, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306210, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306273, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306298, "dur": 294, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306595, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306629, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306717, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197306741, "dur": 366, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307111, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307144, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307242, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307277, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307305, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307323, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307442, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307506, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307574, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197307613, "dur": 826, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197308442, "dur": 70, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197308518, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197308520, "dur": 25, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197308547, "dur": 628, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309179, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309182, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309242, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309244, "dur": 100, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309349, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309389, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309392, "dur": 101, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309500, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309503, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309561, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309841, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309889, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309918, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197309934, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197310044, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197310062, "dur": 512, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197310578, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197310610, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197310612, "dur": 66977, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197377603, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197377612, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197377704, "dur": 1949, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197379655, "dur": 5811, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197385475, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197385488, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197385546, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197385549, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197385783, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197385812, "dur": 335, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386151, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386189, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386400, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386436, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386549, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386583, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386587, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386681, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386760, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386770, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197386860, "dur": 277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387144, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387146, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387209, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387212, "dur": 517, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387736, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387797, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387799, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387903, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387956, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197387988, "dur": 671, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197388661, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197388731, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197388736, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197388779, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197389024, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197389053, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197389212, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197389262, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197389557, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197389587, "dur": 2314, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197391905, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197391959, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197391962, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392010, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392046, "dur": 65, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392114, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392161, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392163, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392208, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392238, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392286, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392322, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392338, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392353, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392411, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392446, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392475, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392509, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392529, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392559, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392605, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392634, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392656, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392729, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392772, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392785, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392797, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392803, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392830, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392863, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392913, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392915, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392963, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392965, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392985, "dur": 7, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197392993, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393000, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393011, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393018, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393073, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393152, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393171, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393198, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393413, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393462, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393464, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393514, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393517, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393549, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393576, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393763, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393787, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393814, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393843, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393896, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393943, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393945, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197393985, "dur": 177, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394171, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394175, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394250, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394252, "dur": 117, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394376, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394408, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394434, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394465, "dur": 271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394740, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394775, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394802, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394920, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394946, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394971, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197394995, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395077, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395094, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395116, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395128, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395189, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395205, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395219, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395244, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395263, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395282, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395318, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395319, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395339, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395355, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395401, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395427, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395452, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395536, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395556, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395575, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197395592, "dur": 86688, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197482289, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197482293, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197482338, "dur": 24, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197482364, "dur": 38941, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197521314, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197521321, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197521365, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197521369, "dur": 85467, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197606843, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197606849, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197606928, "dur": 33, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197606963, "dur": 7509, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197614483, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197614491, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197614605, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197614613, "dur": 3513, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197618134, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197618209, "dur": 44, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197618254, "dur": 56601, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197674864, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197674871, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197674913, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197674917, "dur": 822, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197675746, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197675752, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197675819, "dur": 33, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197675853, "dur": 1569, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197677435, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197677441, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197677525, "dur": 1201, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130197678731, "dur": 9660, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 30272, "tid": 765, "ts": 1754130197703430, "dur": 1769, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 30272, "tid": 8589934592, "ts": 1754130197261250, "dur": 50736, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754130197311988, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754130197311991, "dur": 1377, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 30272, "tid": 765, "ts": 1754130197705201, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 30272, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130197250195, "dur": 439605, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130197252640, "dur": 5222, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130197689903, "dur": 6273, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130197693979, "dur": 50, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130197696294, "dur": 29, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 30272, "tid": 765, "ts": 1754130197705210, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754130197269461, "dur": 1473, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197270942, "dur": 716, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197271758, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754130197271810, "dur": 654, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197272682, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130197273684, "dur": 2009, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130197275740, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4641433ED6B9F892.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130197276857, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754130197277142, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130197279958, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_0F50152946DB09D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130197285269, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754130197286161, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754130197272487, "dur": 17424, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197289922, "dur": 386111, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197676048, "dur": 468, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197676560, "dur": 275, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197677028, "dur": 77, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197677381, "dur": 114, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197677540, "dur": 2424, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754130197272217, "dur": 17714, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197290002, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_43A3C34EEA8A405D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754130197290236, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197290335, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4641433ED6B9F892.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754130197290418, "dur": 288, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4641433ED6B9F892.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754130197290746, "dur": 727, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754130197291490, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754130197291622, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754130197291735, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754130197291851, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754130197291921, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754130197292006, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754130197292080, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197292146, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197292982, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197293786, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197294777, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197295379, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197296142, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197296626, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197297407, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197298099, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197298916, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197299381, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197300450, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197301023, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197301164, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197301374, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197302049, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197302850, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197303911, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754130197304223, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754130197305055, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197305130, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197305567, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197306408, "dur": 1296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197307705, "dur": 77805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197385512, "dur": 7961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754130197393474, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197393558, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197393614, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197393961, "dur": 1273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130197395264, "dur": 280719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197272273, "dur": 17681, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197289966, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754130197290215, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197290415, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754130197290631, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754130197290743, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130197290867, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130197290970, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754130197291219, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130197291426, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130197291548, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754130197291733, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754130197291830, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754130197292007, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130197292116, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130197292241, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130197292297, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197293097, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197293961, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197294759, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197296053, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197296874, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\TriggerEvent2DUnit.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754130197296874, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197298470, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197299094, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197299594, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197300447, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197300557, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197301287, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197302029, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197302658, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754130197302808, "dur": 1049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754130197303858, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197303985, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197304040, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.2D.Internal.ref.dll_EF7AD60E316C51AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754130197304201, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197304559, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197304749, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197305518, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754130197305697, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197306399, "dur": 1262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197307662, "dur": 73191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197380874, "dur": 8348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754130197389223, "dur": 3206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197392591, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197392684, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197392789, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197393157, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197393332, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197393687, "dur": 1382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130197395105, "dur": 280881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197272747, "dur": 17405, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197290167, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754130197290298, "dur": 426, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754130197290726, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130197290805, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130197291003, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130197291123, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130197291413, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130197291488, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754130197291763, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130197291971, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130197292235, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197293049, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197293814, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197294373, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197295574, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197296693, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197297678, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197298429, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197299278, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197300053, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197300707, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197301391, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197302055, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197302840, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754130197303126, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754130197304110, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754130197304244, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197304546, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197304802, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197305559, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197306389, "dur": 1289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197307679, "dur": 73262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197380965, "dur": 8067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754130197389033, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130197389161, "dur": 6144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754130197395410, "dur": 280632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197272315, "dur": 17653, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197289979, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130197290131, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197290308, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A5A0099F491A8369.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130197290414, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1983ABD9EB6C4B5C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130197290652, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130197290872, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754130197291005, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130197291204, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754130197291389, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130197291458, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130197291844, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130197292075, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130197292275, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130197292363, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197293789, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197294747, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197295308, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197296196, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197296877, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197297432, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197298016, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197298726, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197299142, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197299736, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197300735, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197301203, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197301318, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197302068, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197302725, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130197303033, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754130197304884, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197304972, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130197305136, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754130197305722, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197306405, "dur": 1268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197307673, "dur": 73305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197380985, "dur": 7744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754130197388732, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130197388827, "dur": 6832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754130197395714, "dur": 280299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197272374, "dur": 17612, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197289998, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130197290124, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197290304, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130197290406, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130197290704, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754130197290953, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754130197291223, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754130197291333, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130197291490, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754130197291591, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130197291706, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754130197291874, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130197291983, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130197292095, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130197292281, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197293474, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197294160, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197294801, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197295293, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197296440, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197296967, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197297906, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197298581, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197299111, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197299718, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197300352, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197301100, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197301420, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197302059, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197302765, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130197303188, "dur": 1279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754130197304467, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197304562, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197304633, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197304708, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197305523, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197306358, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197306748, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130197306839, "dur": 597, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197307439, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197307639, "dur": 8827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197316467, "dur": 64442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197380913, "dur": 7199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754130197388117, "dur": 663, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130197388800, "dur": 6611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754130197395466, "dur": 280583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197272423, "dur": 17584, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197290023, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130197290126, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_7359D068AFC0FC5C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130197290399, "dur": 288, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130197290750, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754130197290973, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754130197291178, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130197291295, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130197291416, "dur": 627, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130197292045, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130197292154, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197292939, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197293310, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197293969, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197294449, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197295237, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197296579, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197297692, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197298415, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197299155, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197299870, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197300571, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197301219, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197301333, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197302034, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197302653, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130197302844, "dur": 1747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754130197304705, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130197304850, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754130197305433, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197305638, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130197305776, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754130197306741, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130197306856, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754130197307677, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754130197308091, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754130197308447, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197308508, "dur": 76974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197385495, "dur": 7241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754130197392737, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197393109, "dur": 930, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197394052, "dur": 1295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130197395373, "dur": 280678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197272463, "dur": 17560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197290131, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_78AB31D22221CD7D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130197290199, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197290323, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130197290415, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F073B613EC8B985D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130197290512, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130197290653, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130197290810, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754130197290904, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_B79360A6DA5C7A31.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130197291165, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754130197291412, "dur": 395, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754130197291861, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754130197291957, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16800515999216397072.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754130197292105, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754130197292164, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197292232, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754130197292303, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197293164, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197293921, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197294789, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197295321, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197296259, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197297136, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197297859, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197298423, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197298926, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197299356, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197299820, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197300602, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197301356, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197302038, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197302960, "dur": 1055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197304049, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754130197304229, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197304560, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197304759, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197305531, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197306413, "dur": 1266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197307679, "dur": 73936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197381627, "dur": 6334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dots.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754130197387964, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130197388115, "dur": 7278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754130197395458, "dur": 280628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197272506, "dur": 17531, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197290134, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197290189, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754130197290257, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197290329, "dur": 471, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754130197290904, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130197290998, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130197291078, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130197291252, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130197291496, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754130197291673, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130197291792, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130197291868, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130197292148, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197292676, "dur": 916, "ph": "X", "name": "File", "args": {"detail": "D:\\UNRTIY\\<PERSON><PERSON>\\2022.3.61t2\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754130197292435, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197293987, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197294760, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197295423, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197296535, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197296902, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197297574, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197298114, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197298583, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197299289, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197299904, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197300422, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197300491, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197301212, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197301277, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197302058, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197302798, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754130197303295, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754130197304293, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197304390, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197304545, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197304810, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197305546, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197306384, "dur": 1246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197307633, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754130197308059, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197308144, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754130197308488, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197308545, "dur": 72371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197380925, "dur": 6248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Export.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754130197387175, "dur": 808, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197388001, "dur": 7161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754130197395233, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130197395423, "dur": 280655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197272573, "dur": 17482, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197290069, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_704C40EB5E4EE2B7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130197290181, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130197290328, "dur": 393, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E69732DB14D8F64.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130197290812, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2089C4C973FD212.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130197291102, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130197291390, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754130197291632, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130197291770, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130197292022, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130197292241, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197292320, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197293115, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197293562, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197294066, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197294790, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197295358, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197296621, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197297230, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197297991, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197298795, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197299495, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197300245, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197301156, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197301327, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197302037, "dur": 934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197302971, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197304030, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197304225, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197304562, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197304740, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197305522, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197306363, "dur": 1272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197307635, "dur": 5882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197313518, "dur": 2941, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197316459, "dur": 64465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197380927, "dur": 5276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754130197386204, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197386684, "dur": 7704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754130197394390, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197394502, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197394583, "dur": 1113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130197395718, "dur": 280274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197272606, "dur": 17467, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197290089, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_A8FE29C950FB2CD8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754130197290190, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197290378, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197290433, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9AD432D09FAC516B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754130197290510, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754130197290653, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130197291025, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130197291304, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130197291418, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754130197291636, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130197291770, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130197291930, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130197292205, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130197292333, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197293317, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197294144, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197294767, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197295275, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197296093, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197296667, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197297300, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197297996, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197298289, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197299433, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197300169, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197300922, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197301121, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197301222, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197301406, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197302077, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197302679, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754130197302857, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754130197303735, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197303852, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197304023, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197304214, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754130197304518, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754130197305197, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197305437, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197305557, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197306398, "dur": 1262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197307660, "dur": 73261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197380940, "dur": 6938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754130197387879, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130197388023, "dur": 6994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754130197395085, "dur": 280914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197272630, "dur": 17461, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197290137, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197290443, "dur": 860, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130197291310, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130197291452, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130197291517, "dur": 7635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754130197299236, "dur": 1359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130197300620, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754130197302066, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130197302161, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754130197302656, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130197302789, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754130197303792, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197303902, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130197304076, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197304337, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754130197304856, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197305117, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197305235, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197305546, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197306379, "dur": 1254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197307634, "dur": 752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197308388, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130197308626, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754130197309338, "dur": 71596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197380940, "dur": 6246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754130197387189, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130197387293, "dur": 7996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754130197395392, "dur": 280634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197272651, "dur": 17462, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197290125, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_18D02D41D4689F77.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754130197290359, "dur": 482, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754130197291113, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754130197291545, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754130197291650, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754130197291830, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754130197291980, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754130197292051, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4824701561327399296.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754130197292329, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197293105, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197293846, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197294636, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197295551, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197296488, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197297306, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197298121, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197298855, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197299458, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197300164, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197301195, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197301388, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197302057, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197302827, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754130197303218, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197303306, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754130197304240, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197304363, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197304554, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197304776, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197305536, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197306386, "dur": 1267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197307653, "dur": 73203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197380862, "dur": 5815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754130197386679, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197386887, "dur": 6607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754130197393495, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197393654, "dur": 1228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197394937, "dur": 219261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130197614277, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754130197614208, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754130197614564, "dur": 3693, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754130197618267, "dur": 57828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197272700, "dur": 17432, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197290143, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EA8F9A3BC19611D7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130197290385, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197290492, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130197290594, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130197290782, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130197290850, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130197291274, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130197291361, "dur": 518, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130197291880, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130197291965, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130197292276, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197293209, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197293835, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197294669, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197295269, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197296141, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197296686, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197297747, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197298474, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197299069, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197299574, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197300261, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197300814, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197301382, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197302051, "dur": 790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197302848, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130197303137, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197304036, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197304213, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197304566, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197304730, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197305516, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130197305719, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754130197306282, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197306411, "dur": 1286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197307697, "dur": 73270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197380972, "dur": 4450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754130197385424, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197385551, "dur": 7455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754130197393007, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197393156, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197393282, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197393636, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197394555, "dur": 1003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130197395570, "dur": 280505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197272264, "dur": 17679, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197290144, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130197290267, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197290417, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_38E656AD7AB33EA4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130197290511, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130197290632, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130197290861, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130197291078, "dur": 465, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754130197291643, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130197291733, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130197291832, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130197291912, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130197292143, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130197292258, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197293081, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197293569, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197294556, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197295291, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197296518, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197296866, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197297522, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197298169, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197298569, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197299466, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197299996, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197300681, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197301580, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197302084, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197302667, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130197302844, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197302923, "dur": 992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754130197303916, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197304135, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197304217, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197304550, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197304786, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197305533, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197306430, "dur": 1255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197307686, "dur": 73214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197380942, "dur": 5499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754130197386443, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197386542, "dur": 8255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754130197394800, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197394899, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130197395724, "dur": 280346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197272780, "dur": 17393, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197290399, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130197290491, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197290691, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130197291107, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130197291195, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130197291447, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754130197291553, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130197291824, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130197291899, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17923285327036797829.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130197291980, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17923285327036797829.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130197292063, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130197292267, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197292322, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197292943, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197293409, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197294010, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197294561, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197295325, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197296367, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197296896, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197297548, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197298691, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@2.1.4\\Unity.Collections\\CollectionHelper.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754130197298393, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197300375, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\PendingChanges\\PendingChangesTreeHeaderState.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754130197299598, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197300953, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197301256, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197302031, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197302657, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130197303011, "dur": 2448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754130197305461, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197305532, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197305702, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130197305854, "dur": 1329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754130197307271, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130197307363, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754130197307757, "dur": 73130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197380890, "dur": 6783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Documentation.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754130197387677, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130197387833, "dur": 7489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754130197395399, "dur": 280630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197272811, "dur": 17371, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197290190, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197290367, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197290458, "dur": 430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197290937, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_BE343FF0452B4331.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197291052, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130197291302, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754130197291491, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754130197291768, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130197291832, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130197291939, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10131660672807692368.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130197292021, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130197292208, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130197292277, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197293412, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197294163, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197294717, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197295340, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197296527, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197297256, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197298255, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197298961, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197299766, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197300396, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197300594, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197301427, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197302061, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197302749, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197303064, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197303133, "dur": 1286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197304575, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197304721, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197305520, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197305706, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197305859, "dur": 1522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197307450, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197307558, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197308379, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197308585, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197309392, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197309500, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197309633, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197310075, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130197310177, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197310741, "dur": 171655, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197483691, "dur": 35211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754130197483683, "dur": 36567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197521171, "dur": 160, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130197521425, "dur": 85500, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754130197614197, "dur": 60664, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754130197614178, "dur": 60687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754130197674908, "dur": 964, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1754130197272847, "dur": 17351, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197290209, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130197290294, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130197290348, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197290409, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130197290530, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130197290633, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130197290777, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130197290960, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1754130197291054, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1754130197291302, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1754130197291500, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1754130197291830, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130197291922, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3676569134375922281.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130197292139, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130197292274, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197293351, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197294269, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197294849, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197295339, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197296297, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197297195, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197297938, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197298930, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197300160, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\WebApi\\CredentialsResponse.cs"}}, {"pid": 12345, "tid": 17, "ts": 1754130197299598, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197300716, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197301486, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197302069, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197302708, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130197303246, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197303323, "dur": 1128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754130197304563, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130197304729, "dur": 1225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754130197305954, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197306091, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197306393, "dur": 1274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197307668, "dur": 73223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197380902, "dur": 5428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754130197386331, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197386836, "dur": 7362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754130197394199, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197394311, "dur": 1138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130197395492, "dur": 280576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197272876, "dur": 17339, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197290327, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130197290438, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_192890A86BC12540.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130197290498, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130197290636, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130197290827, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1754130197290959, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754130197291077, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1754130197291311, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130197291450, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130197291571, "dur": 7294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1754130197298925, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130197299050, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197299922, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197300787, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197301379, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197302043, "dur": 874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197302917, "dur": 1097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197304038, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1754130197304137, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197304206, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197304555, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197304767, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197305535, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197306366, "dur": 1262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197307630, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1754130197307998, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197308081, "dur": 72902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197380988, "dur": 10983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754130197391972, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197392249, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197392385, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197392500, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197392583, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197392752, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197393181, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197393620, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197394076, "dur": 1285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130197395403, "dur": 280659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197272910, "dur": 17400, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197290322, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754130197290405, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754130197290783, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130197290944, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1754130197291165, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130197291293, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1754130197291456, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1754130197291774, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130197292088, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130197292282, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197292336, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197293138, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197293667, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197294282, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197294957, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197296027, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197296800, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197297381, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197298193, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197298638, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197299536, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197300367, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197300553, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197301449, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197302073, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197302696, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754130197302968, "dur": 1324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754130197304293, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197304381, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754130197304598, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754130197305380, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197305554, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197306388, "dur": 1282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197307672, "dur": 73209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197380913, "dur": 8366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754130197389283, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130197389353, "dur": 6169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754130197395568, "dur": 280512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197272936, "dur": 17389, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197290396, "dur": 468, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130197290902, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_440D2CBC9242B582.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130197291328, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754130197291611, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754130197291945, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754130197292030, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754130197292133, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754130197292322, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197293080, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197293768, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197294222, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197294847, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197296319, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Interfaces\\IMayRequireVertexColor.cs"}}, {"pid": 12345, "tid": 20, "ts": 1754130197295562, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197297141, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197297819, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197298792, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197299583, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197300025, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197300330, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197301106, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197301246, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197302048, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197302892, "dur": 1130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197304022, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197304199, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130197304540, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197304703, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130197304856, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130197305904, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130197306119, "dur": 1848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130197307968, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197308065, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130197308167, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130197309229, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197309324, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130197309519, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130197309972, "dur": 70958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197380940, "dur": 4695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754130197385639, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197385927, "dur": 7992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754130197393920, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197394094, "dur": 1278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130197395372, "dur": 280652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197272969, "dur": 17381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197290424, "dur": 386, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_96EF797E350252A7.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130197290857, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130197290985, "dur": 533, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130197291531, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1754130197291668, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130197291775, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130197291923, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130197291985, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130197292139, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130197292292, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197293089, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197293573, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197294218, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197295566, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SampleTexture2DNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 21, "ts": 1754130197295029, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197296288, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197297532, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197298652, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197299444, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197299987, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197300747, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197301451, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197302066, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197302738, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130197303171, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754130197303873, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197304103, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_3628369EB48E4C19.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130197304196, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130197304409, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197304543, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197304702, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130197304834, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197304898, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754130197305548, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197306397, "dur": 1266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197307663, "dur": 73204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197380882, "dur": 8664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754130197389548, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130197389708, "dur": 5949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754130197395706, "dur": 280395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197272995, "dur": 17383, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197290387, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130197290480, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130197290816, "dur": 479, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130197291316, "dur": 386, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1754130197291766, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130197291904, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130197292075, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130197292232, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197293120, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197293538, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197294797, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197295253, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197296656, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\Tiling\\TileRangeExpansionJob.cs"}}, {"pid": 12345, "tid": 22, "ts": 1754130197296170, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197297178, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197297653, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197298267, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197299176, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197300053, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197300831, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197301235, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197302029, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197302652, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130197302787, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754130197303954, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197304044, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130197304200, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130197304520, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754130197305312, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197305550, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130197305728, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754130197306396, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 22, "ts": 1754130197307075, "dur": 190, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197307732, "dur": 69971, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 22, "ts": 1754130197380852, "dur": 5863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Newtonsoft.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754130197386720, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197386825, "dur": 6670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754130197393496, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197393623, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197394317, "dur": 1134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130197395471, "dur": 280588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197273016, "dur": 17377, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197290492, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197290692, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754130197291112, "dur": 372, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1754130197291501, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1754130197291631, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130197291770, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130197291936, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9630950048426576611.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130197292073, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130197292159, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197292236, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130197292346, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197293508, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197294219, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197294707, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197295561, "dur": 644, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Math\\Basic\\DivideNode.cs"}}, {"pid": 12345, "tid": 23, "ts": 1754130197295326, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197296594, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197297269, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197297693, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197299482, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Pooling\\GenericPool.cs"}}, {"pid": 12345, "tid": 23, "ts": 1754130197298586, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197300249, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197300724, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197301350, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197302036, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197302982, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197304033, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197304218, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197304550, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197304793, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197305542, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197306371, "dur": 1275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197307647, "dur": 73223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197380900, "dur": 4744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754130197385648, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197386290, "dur": 7552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754130197393935, "dur": 1177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197395117, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130197395419, "dur": 280628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197273041, "dur": 17368, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197290435, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754130197290867, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1754130197291217, "dur": 458, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1754130197291676, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130197291782, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130197291918, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130197292077, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130197292305, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130197292373, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197293598, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197294313, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197295036, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197296191, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197296943, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197297523, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197298210, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197298771, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197299429, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197299860, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197300624, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197301375, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197302045, "dur": 857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197302903, "dur": 1115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197304018, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197304200, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197304540, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197304705, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197305133, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197305552, "dur": 857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197306409, "dur": 1268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197307677, "dur": 73228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197380918, "dur": 7838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754130197388759, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130197388870, "dur": 6208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754130197395124, "dur": 280864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130197686721, "dur": 1252, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 30272, "tid": 765, "ts": 1754130197706067, "dur": 2757, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 30272, "tid": 765, "ts": 1754130197708872, "dur": 1597, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 30272, "tid": 765, "ts": 1754130197701777, "dur": 9226, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}