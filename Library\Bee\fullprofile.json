{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 30272, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 30272, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 30272, "tid": 639, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 30272, "tid": 639, "ts": 1754129878235325, "dur": 753, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 30272, "tid": 639, "ts": 1754129878239290, "dur": 500, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 30272, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 30272, "tid": 1, "ts": 1754129877882143, "dur": 5464, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754129877887609, "dur": 25611, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754129877913229, "dur": 23284, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 30272, "tid": 639, "ts": 1754129878239793, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 30272, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877877145, "dur": 927, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877878074, "dur": 350406, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877878819, "dur": 1993, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877880825, "dur": 1724, "ph": "X", "name": "ProcessMessages 20506", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877882552, "dur": 490, "ph": "X", "name": "ReadAsync 20506", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883047, "dur": 15, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883064, "dur": 63, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883131, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883133, "dur": 30, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883169, "dur": 57, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883228, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883257, "dur": 26, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883285, "dur": 31, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883318, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883334, "dur": 42, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883379, "dur": 24, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883405, "dur": 30, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883436, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883454, "dur": 13, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883469, "dur": 47, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883517, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883546, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883568, "dur": 14, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883584, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883600, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883623, "dur": 16, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883640, "dur": 26, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883668, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883689, "dur": 18, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883709, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883728, "dur": 25, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883754, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883755, "dur": 19, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883776, "dur": 13, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883791, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883806, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883821, "dur": 14, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883837, "dur": 13, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883851, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883866, "dur": 46, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883914, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883941, "dur": 20, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883963, "dur": 10, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883975, "dur": 13, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877883989, "dur": 14, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884004, "dur": 20, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884025, "dur": 15, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884042, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884057, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884072, "dur": 15, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884089, "dur": 29, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884119, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884137, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884152, "dur": 13, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884166, "dur": 16, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884183, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884208, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884211, "dur": 71, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884287, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884289, "dur": 47, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884337, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884341, "dur": 50, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884394, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884397, "dur": 37, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884436, "dur": 1, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884437, "dur": 28, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884469, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884494, "dur": 43, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884539, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884566, "dur": 34, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884602, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884627, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884652, "dur": 23, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884677, "dur": 34, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884714, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884735, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884739, "dur": 50, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884790, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884821, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884844, "dur": 17, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884862, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884878, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884895, "dur": 29, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884927, "dur": 48, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877884978, "dur": 20, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885000, "dur": 23, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885026, "dur": 22, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885050, "dur": 44, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885097, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885132, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885134, "dur": 25, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885160, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885162, "dur": 41, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885206, "dur": 54, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885262, "dur": 28, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885291, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885293, "dur": 19, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885314, "dur": 26, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885341, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885368, "dur": 43, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885415, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885417, "dur": 58, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885477, "dur": 1, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885480, "dur": 48, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885531, "dur": 63, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885596, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885598, "dur": 46, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885646, "dur": 52, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885701, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885703, "dur": 39, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885744, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885747, "dur": 47, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885797, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885820, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885868, "dur": 53, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885925, "dur": 3, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885929, "dur": 64, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877885997, "dur": 36, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886035, "dur": 26, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886065, "dur": 29, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886098, "dur": 33, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886132, "dur": 34, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886168, "dur": 24, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886193, "dur": 23, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886218, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886234, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886263, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886282, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886314, "dur": 45, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886362, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886363, "dur": 33, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886398, "dur": 89, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886490, "dur": 25, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886519, "dur": 43, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886565, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886567, "dur": 32, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886601, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886602, "dur": 34, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886638, "dur": 13, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886653, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886675, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886697, "dur": 144, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886842, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886843, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886877, "dur": 17, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886895, "dur": 15, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886911, "dur": 17, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886930, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886944, "dur": 25, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886971, "dur": 14, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877886986, "dur": 71, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887059, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887076, "dur": 15, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887092, "dur": 15, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887109, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887129, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887145, "dur": 14, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887161, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887182, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887199, "dur": 33, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887233, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887249, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887264, "dur": 20, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887285, "dur": 18, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887307, "dur": 14, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887323, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887344, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887362, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887400, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887422, "dur": 25, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887449, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887450, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887474, "dur": 37, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887513, "dur": 42, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887557, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887579, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887611, "dur": 14, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887627, "dur": 63, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887692, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887710, "dur": 31, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887743, "dur": 13, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887758, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887774, "dur": 12, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887787, "dur": 14, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887803, "dur": 61, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887865, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887883, "dur": 18, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887903, "dur": 16, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887921, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887949, "dur": 13, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887964, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877887987, "dur": 36, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888024, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888046, "dur": 18, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888065, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888067, "dur": 11, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888080, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888096, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888117, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888134, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888159, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888174, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888194, "dur": 15, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888210, "dur": 13, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888224, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888226, "dur": 13, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888241, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888257, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888274, "dur": 13, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888289, "dur": 14, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888304, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888319, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888333, "dur": 14, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888350, "dur": 12, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888364, "dur": 16, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888382, "dur": 12, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888396, "dur": 12, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888410, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888428, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888444, "dur": 14, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888459, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888461, "dur": 13, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888475, "dur": 14, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888491, "dur": 13, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888506, "dur": 14, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888521, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888539, "dur": 14, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888555, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888570, "dur": 15, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888587, "dur": 13, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888602, "dur": 22, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888627, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888630, "dur": 71, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888702, "dur": 1, "ph": "X", "name": "ProcessMessages 1179", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888704, "dur": 34, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888741, "dur": 29, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888773, "dur": 34, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888810, "dur": 33, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888844, "dur": 21, "ph": "X", "name": "ReadAsync 1210", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888866, "dur": 39, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888907, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888930, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888956, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888979, "dur": 16, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877888997, "dur": 25, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889024, "dur": 33, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889058, "dur": 14, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889074, "dur": 32, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889107, "dur": 33, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889142, "dur": 26, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889170, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889195, "dur": 21, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889218, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889234, "dur": 23, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889258, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889277, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889300, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889323, "dur": 27, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889352, "dur": 25, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889379, "dur": 23, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889405, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889423, "dur": 24, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889449, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889473, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889499, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889523, "dur": 16, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889542, "dur": 19, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889563, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889580, "dur": 13, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889595, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889614, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889632, "dur": 24, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889657, "dur": 13, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889671, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889672, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889693, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889718, "dur": 19, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889739, "dur": 26, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889766, "dur": 46, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889815, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889817, "dur": 36, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889855, "dur": 1, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889856, "dur": 20, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889878, "dur": 17, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889897, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889917, "dur": 50, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877889974, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890034, "dur": 1, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890036, "dur": 86, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890126, "dur": 31, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890162, "dur": 1, "ph": "X", "name": "ProcessMessages 158", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890165, "dur": 46, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890214, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890218, "dur": 48, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890270, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890272, "dur": 55, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890331, "dur": 5, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890338, "dur": 70, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890411, "dur": 5, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890419, "dur": 94, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890518, "dur": 157, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890677, "dur": 1, "ph": "X", "name": "ProcessMessages 1654", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890679, "dur": 45, "ph": "X", "name": "ReadAsync 1654", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890727, "dur": 7, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890736, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890764, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890790, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890843, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890872, "dur": 60, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890933, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890934, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877890972, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891050, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891092, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891094, "dur": 28, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891125, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891210, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891245, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891247, "dur": 36, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891284, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891286, "dur": 63, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891352, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891398, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891437, "dur": 24, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891464, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891488, "dur": 131, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891621, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891659, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891684, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891706, "dur": 19, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891729, "dur": 108, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891839, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891854, "dur": 25, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891881, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891905, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877891928, "dur": 103, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892034, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892055, "dur": 18, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892076, "dur": 18, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892097, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892115, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892179, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892198, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892214, "dur": 18, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892234, "dur": 15, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892251, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892331, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892383, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892386, "dur": 39, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892427, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892429, "dur": 54, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892489, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892537, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892539, "dur": 32, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892573, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892576, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892623, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892625, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892681, "dur": 35, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892718, "dur": 49, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892771, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892820, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892821, "dur": 38, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892861, "dur": 51, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892915, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892940, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892958, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877892976, "dur": 47, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893024, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893041, "dur": 14, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893057, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893072, "dur": 57, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893131, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893149, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893166, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893168, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893185, "dur": 16, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893203, "dur": 13, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893217, "dur": 51, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893270, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893285, "dur": 14, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893303, "dur": 47, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893351, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893366, "dur": 14, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893382, "dur": 15, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893399, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893414, "dur": 15, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893430, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893432, "dur": 13, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893446, "dur": 14, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893461, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893529, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893544, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893562, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893578, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893635, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893651, "dur": 15, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893667, "dur": 14, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893682, "dur": 15, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893698, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893754, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893771, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893788, "dur": 13, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893802, "dur": 57, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893861, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893880, "dur": 19, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893900, "dur": 43, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893945, "dur": 44, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877893991, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894021, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894023, "dur": 38, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894064, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894066, "dur": 40, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894108, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894109, "dur": 88, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894200, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894229, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894231, "dur": 39, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894272, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894275, "dur": 34, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894312, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894314, "dur": 32, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894349, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894372, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894374, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894397, "dur": 32, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894432, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894434, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894474, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894495, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894511, "dur": 15, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894528, "dur": 70, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894600, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894628, "dur": 41, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894673, "dur": 39, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894715, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894718, "dur": 109, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894828, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894880, "dur": 33, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894916, "dur": 20, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894938, "dur": 56, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877894996, "dur": 29, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895027, "dur": 18, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895047, "dur": 22, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895070, "dur": 119, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895195, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895255, "dur": 2, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895257, "dur": 25, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895286, "dur": 58, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895346, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895456, "dur": 17, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895475, "dur": 15, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895491, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895492, "dur": 17, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895511, "dur": 15, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895527, "dur": 167, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895695, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895745, "dur": 82, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895830, "dur": 44, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895875, "dur": 88, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895966, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877895986, "dur": 19, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896007, "dur": 37, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896045, "dur": 47, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896094, "dur": 96, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896194, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896248, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896250, "dur": 33, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896285, "dur": 83, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896371, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896410, "dur": 27, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896438, "dur": 45, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896485, "dur": 40, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896528, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896530, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896570, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896599, "dur": 20, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896622, "dur": 15, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896639, "dur": 78, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896720, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896751, "dur": 24, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896778, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896798, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896824, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896826, "dur": 49, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896878, "dur": 38, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896917, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896919, "dur": 33, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896954, "dur": 36, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877896993, "dur": 113, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897109, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897163, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897165, "dur": 23, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897190, "dur": 28, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897224, "dur": 44, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897272, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897273, "dur": 32, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897309, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897310, "dur": 45, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897357, "dur": 2, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897360, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897383, "dur": 33, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897419, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897436, "dur": 113, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897551, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897577, "dur": 14, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897592, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897610, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897613, "dur": 32, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897647, "dur": 85, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897735, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897752, "dur": 45, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897801, "dur": 1, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897803, "dur": 98, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897903, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897905, "dur": 53, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897961, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897963, "dur": 32, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877897998, "dur": 25, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898026, "dur": 31, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898059, "dur": 39, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898101, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898151, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898237, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898241, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898317, "dur": 2, "ph": "X", "name": "ProcessMessages 1257", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898321, "dur": 77, "ph": "X", "name": "ReadAsync 1257", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898402, "dur": 2, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898405, "dur": 42, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898451, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898452, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898484, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898536, "dur": 43, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898580, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898582, "dur": 47, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898632, "dur": 29, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898663, "dur": 43, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898707, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898709, "dur": 18, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898729, "dur": 18, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898749, "dur": 25, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898776, "dur": 43, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898821, "dur": 38, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898865, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898867, "dur": 56, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898928, "dur": 41, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877898970, "dur": 130, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899106, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899148, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899149, "dur": 41, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899192, "dur": 43, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899240, "dur": 28, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899270, "dur": 2, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899273, "dur": 16, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899291, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899317, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899354, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899359, "dur": 62, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899424, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899426, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899477, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899479, "dur": 40, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899520, "dur": 31, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899554, "dur": 33, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899588, "dur": 19, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899608, "dur": 20, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899630, "dur": 35, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899666, "dur": 14, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899682, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899709, "dur": 16, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899725, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899728, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899747, "dur": 26, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899774, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899798, "dur": 13, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899813, "dur": 137, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899951, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899973, "dur": 12, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877899987, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900008, "dur": 15, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900025, "dur": 19, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900045, "dur": 16, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900063, "dur": 21, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900085, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900127, "dur": 28, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900157, "dur": 18, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900177, "dur": 53, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900232, "dur": 41, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900275, "dur": 15, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900291, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900311, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900335, "dur": 29, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900367, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900369, "dur": 78, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900449, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900474, "dur": 92, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900568, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900625, "dur": 287, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877900914, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901007, "dur": 3, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901011, "dur": 46, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901063, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901068, "dur": 45, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901116, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901118, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901171, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901173, "dur": 60, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901234, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901237, "dur": 52, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901292, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901295, "dur": 66, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901363, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901365, "dur": 55, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901423, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901424, "dur": 37, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901464, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901466, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901521, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901523, "dur": 45, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901572, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901596, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901614, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901637, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901666, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901711, "dur": 27, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901740, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901780, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901818, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901820, "dur": 60, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901890, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901893, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901952, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877901955, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902015, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902016, "dur": 49, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902068, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902071, "dur": 46, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902121, "dur": 49, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902174, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902177, "dur": 53, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902233, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902235, "dur": 55, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902293, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902296, "dur": 66, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902364, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902368, "dur": 55, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902425, "dur": 5, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902431, "dur": 57, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902490, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902494, "dur": 61, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902558, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902561, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902594, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902618, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902668, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902671, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902706, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902779, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902843, "dur": 7, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902852, "dur": 31, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902885, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902887, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902942, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902992, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877902995, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877903044, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877903047, "dur": 6957, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877910009, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877910032, "dur": 166, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877910203, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877910243, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877910284, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877910309, "dur": 889, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877911202, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877911232, "dur": 2102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913338, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913384, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913386, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913438, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913441, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913471, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913508, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913526, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913737, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913763, "dur": 192, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913960, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877913962, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914003, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914036, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914039, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914089, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914130, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914215, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914217, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914264, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914301, "dur": 483, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914788, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914790, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914847, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914849, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914967, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877914972, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915033, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915035, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915110, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915112, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915185, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915189, "dur": 64, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915258, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915260, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915292, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915337, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915387, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915476, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915510, "dur": 156, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915670, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915716, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915719, "dur": 49, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915772, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915774, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915806, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915849, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915850, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915885, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915910, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877915958, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916003, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916005, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916054, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916055, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916084, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916112, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916132, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916152, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916172, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916189, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916206, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916223, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916341, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916359, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916598, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916617, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916635, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916653, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916670, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916688, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916706, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916742, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916756, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916787, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916823, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916844, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916846, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916927, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916948, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877916983, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917008, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917027, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917090, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917118, "dur": 229, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917348, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917373, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917394, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917439, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917462, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917494, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917521, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917524, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917594, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917620, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917724, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917743, "dur": 220, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917966, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877917993, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918019, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918039, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918057, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918109, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918130, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918148, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918402, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918434, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877918457, "dur": 563, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919023, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919084, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919088, "dur": 83, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919176, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919178, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919214, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919274, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919308, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919414, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919458, "dur": 65, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919528, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919562, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919644, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919671, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919723, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919748, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919783, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919895, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919918, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877919987, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877920005, "dur": 560, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877920566, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877920586, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877920590, "dur": 64418, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877985016, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877985020, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877985055, "dur": 1266, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877986323, "dur": 4487, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877990816, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877990853, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877990856, "dur": 908, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877991771, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877991776, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877991835, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877991837, "dur": 127, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877991968, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992022, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992025, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992094, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992140, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992170, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992415, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992466, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992515, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992558, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992626, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992648, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877992976, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993012, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993042, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993205, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993243, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993245, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993328, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993362, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993389, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993444, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993468, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993489, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993529, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993568, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993572, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993614, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993616, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993644, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993703, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993705, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993752, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993754, "dur": 229, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877993987, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994018, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994048, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994081, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994212, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994262, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994317, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994343, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994480, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994513, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994614, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994616, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994683, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994685, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994721, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994765, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994833, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994835, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994880, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994932, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877994985, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995012, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995066, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995082, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995110, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995153, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995154, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995197, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995255, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995256, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995312, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995315, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877995340, "dur": 688, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877996032, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877996079, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877996110, "dur": 1039, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877997151, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877997175, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877997177, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877997193, "dur": 549, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877997745, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877997786, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877997820, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877997990, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998030, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998068, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998136, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998163, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998204, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998227, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998398, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998436, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998439, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998485, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998487, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998535, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998536, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998586, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998667, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998744, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998746, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998807, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998843, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998891, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998930, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877998974, "dur": 51, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999030, "dur": 6, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999037, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999104, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999108, "dur": 75, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999185, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999190, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999229, "dur": 74, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999312, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999354, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999400, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999402, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999450, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999572, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999575, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129877999622, "dur": 45220, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878044852, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878044859, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878044889, "dur": 26, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878044915, "dur": 55528, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878100450, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878100454, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878100487, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878100489, "dur": 76659, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878177158, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878177162, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878177198, "dur": 21, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878177220, "dur": 5834, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878183057, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878183086, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878183088, "dur": 1397, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878184489, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878184525, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878184537, "dur": 34934, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878219479, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878219483, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878219534, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878219537, "dur": 572, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878220116, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878220122, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878220198, "dur": 34, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878220234, "dur": 749, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878220986, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878221012, "dur": 594, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129878221609, "dur": 6789, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 30272, "tid": 639, "ts": 1754129878239803, "dur": 879, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129877632603, "dur": 303960, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129877936566, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129877936569, "dur": 1456, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 30272, "tid": 639, "ts": 1754129878240684, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 30272, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129877622411, "dur": 606975, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129877624723, "dur": 4588, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129878229434, "dur": 3501, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129878231468, "dur": 34, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129878233000, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 30272, "tid": 639, "ts": 1754129878240690, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754129877642265, "dur": 1278, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129877643554, "dur": 636, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129877644286, "dur": 50, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754129877644336, "dur": 570, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129877645806, "dur": 234021, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129877881308, "dur": 1488, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129877882839, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129877883067, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129877884580, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754129877884731, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129877884962, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129877885311, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754129877885879, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129877886183, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129877887277, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129877888792, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129877889766, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754129877889821, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754129877890362, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129877893778, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129877894632, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754129877894757, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129877895126, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754129877895201, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129877897070, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129877897618, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129877898370, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129877898444, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129877899234, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754129877899307, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754129877899397, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129877644925, "dur": 255307, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129877900244, "dur": 319765, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129878220012, "dur": 155, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129878220200, "dur": 138, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129878220367, "dur": 122, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129878220658, "dur": 64, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129878220750, "dur": 1002, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754129877644934, "dur": 255320, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877900413, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129877900477, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877900588, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877900748, "dur": 776, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_F5A419B9F1F8246D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129877901530, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129877901695, "dur": 8164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754129877909860, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877909971, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129877910079, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129877910963, "dur": 2109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754129877913204, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129877913280, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754129877913571, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129877913712, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754129877914448, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877914563, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129877914750, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877914909, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754129877915916, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877916599, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877917321, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877918333, "dur": 68684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877987039, "dur": 5542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754129877992582, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129877992798, "dur": 6201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754129877999099, "dur": 220872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877645039, "dur": 255262, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877900310, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129877900503, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129877900689, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877900790, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129877900913, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754129877901039, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129877901265, "dur": 538, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A6E1C780C67641AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129877901816, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754129877901924, "dur": 448, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754129877902374, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754129877902603, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877903513, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877904397, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877904833, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877905466, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877906053, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877907434, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877908381, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877909418, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877909841, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877910596, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877911360, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877911869, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877912041, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877912127, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877912721, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877913181, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877913605, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129877913907, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754129877914668, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877914820, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877914883, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877915470, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877915526, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129877915744, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877915918, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754129877916439, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877916614, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877917303, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877918323, "dur": 68705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877987047, "dur": 8640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754129877995688, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877995858, "dur": 1947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877997807, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754129877997864, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877997976, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129877998748, "dur": 221215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877645086, "dur": 255231, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877900331, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129877900490, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877900571, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129877900628, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877900723, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129877900844, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129877900972, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129877901039, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754129877901134, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754129877901353, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129877901430, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129877901671, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754129877901746, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754129877902115, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129877902309, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129877902454, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129877902537, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129877902663, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877903623, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877904375, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877905212, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877906044, "dur": 782, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Util\\MipSamplingModes.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754129877905792, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877907122, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877907741, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877908429, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877908846, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877909230, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877909641, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877910156, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877910592, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877911814, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877912789, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877913204, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877913612, "dur": 1247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877914862, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877915015, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877915481, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877915593, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877915744, "dur": 836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877916581, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877917249, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877918223, "dur": 19538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877937762, "dur": 1719, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877939482, "dur": 47589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877987086, "dur": 4426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754129877991566, "dur": 6172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754129877997808, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754129877997905, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877998691, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754129877998756, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129877998833, "dur": 221199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877644971, "dur": 255300, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877900283, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877900352, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129877900499, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_27474F036FBEA425.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129877900737, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129877900975, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129877901167, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129877901461, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754129877901810, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754129877902170, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129877902386, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129877902475, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129877902671, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877903552, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877904327, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877904745, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877905410, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877906125, "dur": 1431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877907556, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877908201, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877909321, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877909778, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877910505, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877911754, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877912773, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877913191, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877913609, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877914567, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129877915003, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877915081, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754129877915972, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877916610, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877917312, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877918311, "dur": 68741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877987056, "dur": 4972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754129877992029, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877992172, "dur": 6448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754129877998685, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129877998773, "dur": 221213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877645023, "dur": 255260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877900294, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129877900710, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_96EF797E350252A7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129877900803, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129877900985, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129877901167, "dur": 489, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754129877901676, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754129877901888, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129877902104, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129877902190, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129877902266, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129877902355, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129877902505, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754129877902610, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877903493, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877904586, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877905570, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877906817, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877907537, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877908276, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877908947, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\ElementAdderMenu\\IElementAdder.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754129877908874, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877910211, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877910597, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877911043, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877911626, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877911823, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877912694, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877913186, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877913606, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129877913992, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877914871, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877915459, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877915528, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129877915792, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877916614, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877917345, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877918277, "dur": 69252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877987536, "dur": 3943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754129877991480, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877991571, "dur": 6560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754129877998135, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877998315, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129877998941, "dur": 221053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877645192, "dur": 255162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877900369, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9EEBEC0FC3FAC728.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129877900497, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129877900587, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877900793, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129877900915, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129877901166, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754129877901463, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129877901753, "dur": 386, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754129877902202, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129877902277, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10131660672807692368.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129877902373, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129877902526, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877902601, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877903486, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877904406, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877905117, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877905910, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877906830, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877907762, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877908412, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877909134, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877909746, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877910287, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877910989, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877911600, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877911657, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877912494, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877912663, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877913180, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877913586, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129877913843, "dur": 2027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754129877915870, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877915967, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129877916114, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754129877916611, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877916791, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877917276, "dur": 1083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877918359, "dur": 68637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877987005, "dur": 5280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754129877992286, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877992391, "dur": 6416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754129877998808, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129877998910, "dur": 221095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877645127, "dur": 255205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877900337, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41F9674DB235B210.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129877900471, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_78AB31D22221CD7D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129877900587, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877900727, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129877900862, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129877901041, "dur": 374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129877901454, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754129877901759, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754129877902152, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129877902359, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129877902548, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129877902658, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877902720, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877903696, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877904385, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877905299, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877906002, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877907079, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877907910, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877908697, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877909910, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877910681, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877911571, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877911822, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877912680, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877913168, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877913617, "dur": 1269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877914887, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877915472, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877915595, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877915754, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877916580, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129877916714, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877917285, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877918349, "dur": 69572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877987924, "dur": 4791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754129877992759, "dur": 5836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754129877998596, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129877998780, "dur": 221196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877645174, "dur": 255169, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877900635, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877900752, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129877901095, "dur": 407, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754129877901756, "dur": 393, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129877902167, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754129877902327, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129877902503, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129877902690, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877903458, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877904569, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877905459, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877906053, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877907340, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877908150, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877909176, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877909793, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877910875, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877911583, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877911653, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877912741, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877913194, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877913602, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129877913963, "dur": 750, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877914723, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754129877915847, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877916602, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877917338, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877918290, "dur": 68694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877987020, "dur": 5953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754129877992974, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129877993089, "dur": 6186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754129877999343, "dur": 220648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877645255, "dur": 255118, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877900391, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_704C40EB5E4EE2B7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129877900490, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129877900635, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877900736, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E69732DB14D8F64.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129877900916, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754129877901197, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129877901331, "dur": 354, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129877901814, "dur": 404, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754129877902219, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129877902387, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129877902713, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877904051, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877904957, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877906139, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Scene\\ObjectNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754129877906106, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877907794, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877908575, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877909161, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877909584, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877910019, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877910755, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877911382, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877912021, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877912178, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877912676, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877913169, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877913582, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129877913933, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129877913991, "dur": 1386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754129877915591, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129877915884, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754129877916526, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877916617, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877917295, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877918342, "dur": 68668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877987026, "dur": 4617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754129877991645, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877991845, "dur": 5522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754129877997368, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877997563, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877998489, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129877999155, "dur": 220843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877645297, "dur": 255095, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877900406, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C0425020FB5520E6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129877900511, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129877900720, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129877900807, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129877900971, "dur": 541, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129877901547, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129877901616, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129877901798, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129877902012, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129877902077, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129877902310, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129877902523, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129877902701, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cloud.gltfast@6.13.1\\Runtime\\Scripts\\AccessorUsage.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754129877902701, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877904477, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877905098, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877905732, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877906813, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877907993, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877908872, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877909245, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877910047, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877910812, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877912035, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877912578, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877912648, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877913159, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877913577, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129877913958, "dur": 1420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129877915526, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129877915876, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129877916611, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129877916720, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129877917075, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877917248, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1754129877917759, "dur": 133, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877918213, "dur": 66537, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1754129877987024, "dur": 4590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Newtonsoft.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754129877991616, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877991738, "dur": 6032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754129877997771, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877998206, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877998322, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129877998958, "dur": 221086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877645336, "dur": 255072, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877900422, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_A8FE29C950FB2CD8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129877900480, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877900628, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877900830, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129877900921, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129877901042, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_974E85225CDD9232.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129877901324, "dur": 324, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129877901677, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754129877901818, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754129877902305, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129877902437, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129877902759, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877903811, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877904529, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877905823, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877907439, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877908087, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877908916, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877909797, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877910539, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877911092, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877911769, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877912667, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877913164, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877913591, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129877913965, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129877914025, "dur": 1263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754129877915289, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877915605, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877915765, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877916599, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877917361, "dur": 868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877918229, "dur": 21258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877939488, "dur": 47578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877987073, "dur": 5191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754129877992293, "dur": 852, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877993255, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877993327, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877993489, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877994007, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877994555, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877994631, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877994882, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/glTFast.Export.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754129877995012, "dur": 814, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877995833, "dur": 1975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877997809, "dur": 790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129877998637, "dur": 221332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877645395, "dur": 255036, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877900450, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1A0DCA6DE3746D0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129877900738, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129877901022, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129877901153, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129877901323, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129877901761, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754129877901861, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754129877902154, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129877902264, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17923285327036797829.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129877902402, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129877902618, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877903651, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877904777, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877905287, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877906088, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877907436, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877908069, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877909411, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877909901, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877910476, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877911126, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877911710, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877912604, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877913155, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877913577, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129877914002, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754129877915061, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877915144, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877915220, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877915590, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877915728, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129877915948, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754129877916400, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877916610, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877917354, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877918264, "dur": 68703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877986974, "dur": 5130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754129877992105, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877992284, "dur": 5979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754129877998265, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877998436, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877998497, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129877999371, "dur": 220646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877645458, "dur": 254994, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877900727, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F073B613EC8B985D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129877900871, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129877900988, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754129877901152, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754129877901335, "dur": 546, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754129877901966, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129877902032, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129877902177, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129877902421, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129877902651, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877902709, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877903428, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877904620, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877905930, "dur": 715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Generation\\TargetResources\\FieldDependencies.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754129877905413, "dur": 1849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877907262, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877907968, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877908599, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877909439, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877910080, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877911031, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877911873, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877912225, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877912744, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877913190, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877913603, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129877913897, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877914005, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754129877914939, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877915131, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877915585, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877915732, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877915928, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877916606, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877917330, "dur": 970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877918300, "dur": 68693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877986996, "dur": 5106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Documentation.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754129877992103, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877992278, "dur": 5769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754129877998048, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877998141, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877998253, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129877998883, "dur": 221117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877645544, "dur": 254925, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877900480, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_66FDAEB71878BAE6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129877900742, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9AD432D09FAC516B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129877901012, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129877901210, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754129877901311, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754129877901374, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129877901623, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129877901747, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129877901922, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129877902296, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16800515999216397072.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129877902436, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129877902638, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877903355, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877904517, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877904902, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877905584, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877906601, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877907366, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877908374, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877909168, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877909930, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877910805, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877911200, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877912108, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877912601, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877912742, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877913196, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877913611, "dur": 1251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877914863, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877915462, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877915596, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877915756, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877916590, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877917279, "dur": 1088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877918367, "dur": 68640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877987018, "dur": 4701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754129877991720, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129877991856, "dur": 7195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754129877999143, "dur": 220836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877645596, "dur": 254887, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877900490, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_3728351D5FA9AF42.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129877900644, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877900745, "dur": 410, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129877901174, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754129877901301, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754129877901405, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129877901588, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754129877901687, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129877901747, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129877902042, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754129877902251, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129877902341, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129877902681, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877903658, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877904579, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877905201, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877905777, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877906854, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877908163, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877909310, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877910293, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877911013, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877911813, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877912767, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877913205, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877913619, "dur": 1261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877914880, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877915595, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877915756, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877916583, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877917266, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877918219, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754129877918625, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877918914, "dur": 68132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877987049, "dur": 4343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754129877991393, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877991519, "dur": 5990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754129877997510, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877997813, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129877998727, "dur": 221234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877645646, "dur": 254853, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877900512, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129877900733, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_38E656AD7AB33EA4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129877900975, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754129877901167, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754129877901352, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129877901460, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754129877901686, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129877901871, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754129877902092, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754129877902249, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129877902405, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4824701561327399296.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129877902565, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877903924, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877904686, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877905168, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877906120, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Controls\\ChannelEnumMaskControl.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754129877905734, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877906869, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877907673, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877908447, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877909255, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877910107, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877910507, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877911690, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877912698, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877913173, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877913600, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129877913917, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129877914019, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754129877914762, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877914873, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877914950, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877915485, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877915593, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877915747, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877916579, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129877916729, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754129877917141, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877917266, "dur": 1111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877918377, "dur": 68648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877987028, "dur": 4613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Export.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754129877991642, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877991872, "dur": 6955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754129877998828, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129877998930, "dur": 221036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877645695, "dur": 254826, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877900530, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877900979, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129877901098, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1754129877901229, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129877901534, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877901674, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877901856, "dur": 7834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129877909691, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877909791, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877910586, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877911435, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877912117, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877912355, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877912634, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877913168, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877913584, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877913854, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877913912, "dur": 1311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129877915224, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877915585, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877915734, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877916461, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877916594, "dur": 1167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129877917828, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877917918, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129877918518, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877918617, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129877919199, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877919312, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129877919668, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129877919762, "dur": 67278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877987048, "dur": 5620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754129877992671, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877992764, "dur": 5928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754129877998693, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129877998862, "dur": 221153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877645731, "dur": 254800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877900590, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129877900649, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877900747, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129877901330, "dur": 523, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1754129877901866, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1754129877901952, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129877902172, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1754129877902558, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129877902650, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877902723, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877903658, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877904416, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877904959, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877905557, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877906750, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877907534, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877908242, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877908750, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877909817, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877910616, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877911510, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Runtime\\Editor\\BurstReflection.cs"}}, {"pid": 12345, "tid": 18, "ts": 1754129877911391, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877912725, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877913184, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877913614, "dur": 1246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877914893, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877915474, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877915534, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129877915750, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877916585, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877917247, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877917382, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129877917496, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877918253, "dur": 68725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877986995, "dur": 5879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754129877992875, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877992972, "dur": 5857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754129877998830, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129877998946, "dur": 221067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877645773, "dur": 254771, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877900556, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129877900648, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129877900714, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129877901042, "dur": 506, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129877901551, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129877901620, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129877901694, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129877902034, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129877902167, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1754129877902521, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129877902631, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877903520, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877904483, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877905074, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877905949, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Generation\\Collections\\StructCollection.cs"}}, {"pid": 12345, "tid": 19, "ts": 1754129877905536, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877906826, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877907899, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877908649, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877909445, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877910736, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877911765, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877912714, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877913183, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877913621, "dur": 1243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877914865, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877915481, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877915591, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129877915730, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129877915952, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129877916755, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129877916866, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129877918376, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129877918519, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129877919416, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129877919516, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129877919776, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129877920375, "dur": 124191, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129878046097, "dur": 51419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1754129878046086, "dur": 53121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754129878100022, "dur": 128, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129878100189, "dur": 76697, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754129878182655, "dur": 36503, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1754129878182649, "dur": 36510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1754129878219183, "dur": 694, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1754129877645824, "dur": 254737, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877900622, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877900800, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877900979, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129877901325, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1754129877901708, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129877902030, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1754129877902166, "dur": 472, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129877902639, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877903520, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877904399, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877904946, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877906121, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Utility\\ForLoopStartNode.cs"}}, {"pid": 12345, "tid": 20, "ts": 1754129877905840, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877906850, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877907605, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877908243, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877908853, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877909441, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877910302, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877910901, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877911630, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877912532, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877912640, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877913158, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877913574, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129877913804, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877913961, "dur": 2242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129877916204, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877916457, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129877916570, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877916697, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129877917759, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129877917885, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129877918270, "dur": 68693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877986965, "dur": 6322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754129877993288, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877993503, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877993834, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877994085, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877994277, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877994366, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877994434, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/glTFast.Documentation.Examples.dll"}}, {"pid": 12345, "tid": 20, "ts": 1754129877994613, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877994767, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877994886, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Internal.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1754129877994987, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877995052, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877995830, "dur": 1688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877997557, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877998318, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877998401, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129877999104, "dur": 220860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877645875, "dur": 254713, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877900634, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877900791, "dur": 607, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129877901612, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129877901864, "dur": 387, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129877902252, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3676569134375922281.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129877902342, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129877902512, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129877902756, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877903459, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877904324, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877905058, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877905851, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877907472, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\UniversalRenderPipelineCore.cs"}}, {"pid": 12345, "tid": 21, "ts": 1754129877906956, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877908366, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877909110, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877909635, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877910533, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877911730, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877912723, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877913178, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877913591, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129877913980, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754129877915358, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877915537, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877915600, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877915736, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877916581, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877917248, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877918222, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877918523, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129877918647, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754129877919191, "dur": 67843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877987068, "dur": 3349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754129877990419, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877990543, "dur": 6271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754129877996814, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877996958, "dur": 1193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877998210, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129877998646, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 21, "ts": 1754129877998706, "dur": 221253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877645922, "dur": 254689, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877900721, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1983ABD9EB6C4B5C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129877901131, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877901271, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877901455, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877901552, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877901623, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877901734, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877901813, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877902237, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877902434, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877902541, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129877902659, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877902732, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877903876, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877904601, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877905272, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877906111, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SamplerStateNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 22, "ts": 1754129877905708, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877906984, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877907924, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877908625, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877909064, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877909819, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877910775, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877911657, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877912693, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877913170, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877913584, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129877913890, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754129877915074, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877915262, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129877915555, "dur": 1113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754129877916668, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877917144, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877917256, "dur": 964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877918221, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754129877919046, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754129877919495, "dur": 67537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877987048, "dur": 5212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754129877992261, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877992736, "dur": 5791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754129877998529, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129877998614, "dur": 184044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129878182692, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754129878182661, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754129878182821, "dur": 1414, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754129878184238, "dur": 35769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877645965, "dur": 254674, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877900687, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877900803, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129877901096, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129877901206, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129877901480, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129877901626, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129877902072, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1754129877902197, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129877902274, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9630950048426576611.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129877902385, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129877902581, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877903604, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877904192, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877905216, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877905990, "dur": 706, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Interfaces\\IRectInterface.cs"}}, {"pid": 12345, "tid": 23, "ts": 1754129877905658, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877907254, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877907850, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877908474, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877909293, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877910220, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877910814, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877911557, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877912622, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877913157, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877913576, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129877913885, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129877914769, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877914899, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877915476, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877915536, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877915600, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877915763, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877916592, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877917371, "dur": 870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877918241, "dur": 68734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877986989, "dur": 6267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754129877993406, "dur": 800, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877994228, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 23, "ts": 1754129877994418, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1754129877994534, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1754129877994637, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1754129877994776, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877994951, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877995089, "dur": 1899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877996988, "dur": 1227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877998215, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129877998829, "dur": 221167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877646018, "dur": 254639, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877900657, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129877900797, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129877900910, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129877901106, "dur": 455, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1754129877901635, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129877901858, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1754129877901996, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129877902153, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1754129877902220, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129877902305, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129877902457, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129877902582, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877902692, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877904017, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877904773, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877905317, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877906600, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877907567, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877908456, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877909164, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877909652, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877910280, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877910927, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877911795, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877912808, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877913163, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877913573, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129877913789, "dur": 1745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129877915535, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877915758, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129877915985, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129877916415, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129877916590, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129877917379, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129877917500, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129877918223, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129877918774, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129877919290, "dur": 67787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877987083, "dur": 4756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dots.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754129877991841, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877991915, "dur": 6590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754129877998507, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877998750, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129877998914, "dur": 221118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129878226197, "dur": 1270, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 30272, "tid": 639, "ts": 1754129878241015, "dur": 2736, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 30272, "tid": 639, "ts": 1754129878243821, "dur": 1747, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 30272, "tid": 639, "ts": 1754129878238371, "dur": 7900, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}