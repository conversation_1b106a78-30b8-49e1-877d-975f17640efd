{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 30272, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 30272, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 30272, "tid": 690, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 30272, "tid": 690, "ts": 1754130050036956, "dur": 824, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 30272, "tid": 690, "ts": 1754130050041074, "dur": 506, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 30272, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 30272, "tid": 1, "ts": 1754130049619181, "dur": 3284, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754130049622467, "dur": 21742, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754130049644217, "dur": 22883, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 30272, "tid": 690, "ts": 1754130050041584, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 30272, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049618000, "dur": 7128, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049625130, "dur": 399780, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049625949, "dur": 1761, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049627713, "dur": 1077, "ph": "X", "name": "ProcessMessages 6644", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049628793, "dur": 409, "ph": "X", "name": "ReadAsync 6644", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629205, "dur": 6, "ph": "X", "name": "ProcessMessages 20491", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629212, "dur": 28, "ph": "X", "name": "ReadAsync 20491", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629241, "dur": 45, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629289, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629307, "dur": 35, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629346, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629348, "dur": 283, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629634, "dur": 116, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629751, "dur": 1, "ph": "X", "name": "ProcessMessages 4530", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629753, "dur": 32, "ph": "X", "name": "ReadAsync 4530", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629786, "dur": 32, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629820, "dur": 63, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629885, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629903, "dur": 38, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629943, "dur": 11, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629956, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629977, "dur": 13, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049629992, "dur": 31, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630024, "dur": 35, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630061, "dur": 18, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630080, "dur": 14, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630096, "dur": 15, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630113, "dur": 19, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630134, "dur": 13, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630149, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630209, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630253, "dur": 15, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630269, "dur": 16, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630288, "dur": 122, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630415, "dur": 18, "ph": "X", "name": "ReadAsync 1949", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630434, "dur": 18, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630454, "dur": 14, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630469, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630498, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630514, "dur": 18, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630534, "dur": 14, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630550, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630576, "dur": 19, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630597, "dur": 13, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630612, "dur": 15, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630628, "dur": 74, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630704, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630722, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630748, "dur": 19, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630770, "dur": 7, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630779, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630824, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630849, "dur": 13, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630865, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630886, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630908, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630911, "dur": 30, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630947, "dur": 20, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630967, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630969, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630993, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049630996, "dur": 28, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631027, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631050, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631069, "dur": 17, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631087, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631091, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631110, "dur": 17, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631129, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631131, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631161, "dur": 16, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631179, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631183, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631202, "dur": 14, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631219, "dur": 23, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631246, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631266, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631287, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631304, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631307, "dur": 16, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631328, "dur": 13, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631344, "dur": 20, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631367, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631387, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631393, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631408, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631411, "dur": 13, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631428, "dur": 12, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631446, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631469, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631491, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631522, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631526, "dur": 43, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631571, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631573, "dur": 27, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631604, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631609, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631629, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631661, "dur": 20, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631685, "dur": 26, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631715, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631737, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631741, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631759, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631761, "dur": 26, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631791, "dur": 16, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631814, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631839, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631842, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631865, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631867, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631885, "dur": 1, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631889, "dur": 18, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631909, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631911, "dur": 22, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631935, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631936, "dur": 13, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631951, "dur": 3, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631959, "dur": 18, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631979, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049631981, "dur": 22, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632007, "dur": 16, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632028, "dur": 20, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632052, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632056, "dur": 33, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632091, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632096, "dur": 18, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632120, "dur": 17, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632138, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632141, "dur": 15, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632159, "dur": 17, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632179, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632199, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632203, "dur": 20, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632230, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632249, "dur": 18, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632273, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632275, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632298, "dur": 2, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632300, "dur": 24, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632328, "dur": 17, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632349, "dur": 18, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632371, "dur": 21, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632398, "dur": 17, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632418, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632442, "dur": 17, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632461, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632463, "dur": 11, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632479, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632494, "dur": 17, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632514, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632539, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632541, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632563, "dur": 18, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632585, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632604, "dur": 16, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632621, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632622, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632648, "dur": 23, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632675, "dur": 15, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632691, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632693, "dur": 27, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632722, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632726, "dur": 19, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632748, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632769, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632771, "dur": 26, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632800, "dur": 25, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632826, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632830, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632856, "dur": 10, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632870, "dur": 20, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632894, "dur": 15, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632913, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049632916, "dur": 163, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633084, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633113, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633115, "dur": 18, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633136, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633138, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633165, "dur": 14, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633184, "dur": 17, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633205, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633222, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633223, "dur": 20, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633245, "dur": 2, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633251, "dur": 11, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633266, "dur": 13, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633282, "dur": 17, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633304, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633326, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633346, "dur": 13, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633362, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633386, "dur": 15, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633404, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633406, "dur": 13, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633421, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633424, "dur": 42, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633470, "dur": 14, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633486, "dur": 15, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633505, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633524, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633525, "dur": 13, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633542, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633544, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633563, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633583, "dur": 22, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633608, "dur": 18, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633629, "dur": 13, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633646, "dur": 26, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633675, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633705, "dur": 15, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633725, "dur": 15, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633745, "dur": 35, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633784, "dur": 14, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633802, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633830, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633833, "dur": 40, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633874, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633876, "dur": 21, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633900, "dur": 15, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633918, "dur": 18, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633940, "dur": 13, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633956, "dur": 18, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633980, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049633998, "dur": 38, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634038, "dur": 12, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634056, "dur": 16, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634075, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634097, "dur": 15, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634115, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634136, "dur": 15, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634153, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634175, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634201, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634203, "dur": 12, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634218, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634241, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634243, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634265, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634284, "dur": 23, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634310, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634335, "dur": 16, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634357, "dur": 11, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634371, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634383, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634400, "dur": 20, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634422, "dur": 3, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634428, "dur": 19, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634453, "dur": 15, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634472, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634476, "dur": 14, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634494, "dur": 17, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634513, "dur": 15, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634531, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634550, "dur": 19, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634571, "dur": 17, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634593, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634613, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634638, "dur": 22, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634664, "dur": 16, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634684, "dur": 15, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634702, "dur": 16, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634723, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634742, "dur": 16, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634761, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634781, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634800, "dur": 13, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634815, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634818, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634839, "dur": 10, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634852, "dur": 15, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634870, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634893, "dur": 15, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634911, "dur": 16, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634929, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634951, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634953, "dur": 28, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634985, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049634987, "dur": 24, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635015, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635017, "dur": 22, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635041, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635043, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635068, "dur": 22, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635092, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635095, "dur": 17, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635116, "dur": 24, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635143, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635161, "dur": 18, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635181, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635183, "dur": 19, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635205, "dur": 16, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635224, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635244, "dur": 17, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635262, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635264, "dur": 18, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635284, "dur": 19, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635305, "dur": 5, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635313, "dur": 12, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635331, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635352, "dur": 16, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635372, "dur": 28, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635402, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635405, "dur": 24, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635434, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635436, "dur": 15, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635455, "dur": 27, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635488, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635491, "dur": 24, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635518, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635523, "dur": 26, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635552, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635554, "dur": 16, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635573, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635578, "dur": 29, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635608, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635611, "dur": 20, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635636, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635658, "dur": 16, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635679, "dur": 21, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635703, "dur": 20, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635727, "dur": 17, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635747, "dur": 18, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635769, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635787, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635809, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635811, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635845, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635849, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635875, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635907, "dur": 20, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635928, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635931, "dur": 61, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049635995, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636012, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636014, "dur": 23, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636039, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636042, "dur": 24, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636067, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636069, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636110, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636141, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636146, "dur": 18, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636167, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636169, "dur": 71, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636243, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636245, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636274, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636317, "dur": 19, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636339, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636342, "dur": 32, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636376, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636405, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636408, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636432, "dur": 54, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636489, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636515, "dur": 21, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636539, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636541, "dur": 22, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636566, "dur": 39, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636610, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636636, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636639, "dur": 22, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636667, "dur": 51, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636721, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636747, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636754, "dur": 23, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636782, "dur": 24, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636814, "dur": 31, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636852, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636900, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636905, "dur": 28, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636938, "dur": 48, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049636994, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637023, "dur": 25, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637053, "dur": 43, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637097, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637101, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637125, "dur": 22, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637149, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637153, "dur": 48, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637203, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637208, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637235, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637238, "dur": 33, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637275, "dur": 40, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637319, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637355, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637358, "dur": 24, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637383, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637386, "dur": 104, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637494, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637509, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637514, "dur": 20, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637536, "dur": 15, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637553, "dur": 5, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637559, "dur": 30, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637593, "dur": 72, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637671, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637706, "dur": 3, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637711, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637733, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637737, "dur": 32, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637775, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637796, "dur": 77, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637880, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637918, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637945, "dur": 29, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637976, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049637981, "dur": 27, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638009, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638014, "dur": 20, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638037, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638040, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638060, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638064, "dur": 19, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638093, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638176, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638192, "dur": 14, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638210, "dur": 3, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638214, "dur": 12, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638228, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638231, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638294, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638319, "dur": 2, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638326, "dur": 26, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638354, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638356, "dur": 44, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638404, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638406, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638425, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638429, "dur": 11, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638448, "dur": 61, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638514, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638543, "dur": 18, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638565, "dur": 50, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638620, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638639, "dur": 17, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638662, "dur": 12, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638677, "dur": 11, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638691, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638693, "dur": 52, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638747, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638771, "dur": 15, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638790, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638809, "dur": 41, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638855, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638874, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638876, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638899, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638904, "dur": 46, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638958, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049638981, "dur": 21, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639005, "dur": 15, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639022, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639025, "dur": 54, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639082, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639096, "dur": 19, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639117, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639120, "dur": 14, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639137, "dur": 57, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639197, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639221, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639224, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639246, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639262, "dur": 16, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639281, "dur": 53, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639338, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639368, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639370, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639392, "dur": 46, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639441, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639470, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639490, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639493, "dur": 48, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639546, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639550, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639577, "dur": 20, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639604, "dur": 21, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639627, "dur": 96, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639726, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639746, "dur": 19, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639767, "dur": 14, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639783, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639786, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639814, "dur": 13, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639830, "dur": 66, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639899, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639920, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639943, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639966, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049639983, "dur": 89, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640074, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640094, "dur": 18, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640116, "dur": 16, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640135, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640154, "dur": 88, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640245, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640267, "dur": 17, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640288, "dur": 17, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640309, "dur": 63, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640374, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640394, "dur": 29, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640426, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640446, "dur": 33, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640482, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640484, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640508, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640509, "dur": 20, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640531, "dur": 7, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640539, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640592, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640618, "dur": 3, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640624, "dur": 27, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640652, "dur": 2, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640655, "dur": 17, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640678, "dur": 23, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640704, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640706, "dur": 17, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640726, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640747, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640823, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640857, "dur": 17, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640874, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640876, "dur": 52, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640932, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640950, "dur": 16, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640969, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049640989, "dur": 16, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641007, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641009, "dur": 14, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641030, "dur": 18, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641052, "dur": 16, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641072, "dur": 14, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641089, "dur": 49, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641142, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641162, "dur": 15, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641180, "dur": 22, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641205, "dur": 20, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641228, "dur": 14, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641245, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641266, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641321, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641340, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641343, "dur": 80, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641427, "dur": 19, "ph": "X", "name": "ReadAsync 1446", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641449, "dur": 16, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641469, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641522, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641524, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641547, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641569, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641571, "dur": 16, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641592, "dur": 14, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641607, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641623, "dur": 1, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641625, "dur": 16, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641646, "dur": 14, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641663, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641714, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641736, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641752, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641754, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641777, "dur": 24, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641802, "dur": 3, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641808, "dur": 19, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641832, "dur": 12, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641846, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641848, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641872, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641876, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641927, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641959, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641961, "dur": 30, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641993, "dur": 4, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049641998, "dur": 29, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642028, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642031, "dur": 30, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642065, "dur": 27, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642095, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642173, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642210, "dur": 132, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642347, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642351, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642432, "dur": 400, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642836, "dur": 95, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642933, "dur": 6, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049642941, "dur": 58, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643001, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643005, "dur": 56, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643063, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643066, "dur": 69, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643138, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643140, "dur": 49, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643192, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643193, "dur": 25, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643220, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643262, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643267, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643314, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643315, "dur": 38, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643356, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643357, "dur": 47, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643407, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643408, "dur": 42, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643452, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643490, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643524, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643561, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643603, "dur": 35, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643640, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643667, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643690, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643726, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643730, "dur": 60, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643792, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643794, "dur": 44, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643840, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643842, "dur": 53, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643897, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643899, "dur": 41, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643943, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643945, "dur": 35, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643982, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049643984, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644023, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644026, "dur": 38, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644067, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644068, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644107, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644128, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644151, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644178, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644202, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644234, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644261, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644291, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644292, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644342, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644343, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644388, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644390, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644442, "dur": 54, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644499, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644501, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644540, "dur": 19, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644563, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644589, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049644612, "dur": 4463, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049649079, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049649117, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049649120, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049649164, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049649166, "dur": 1318, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049650489, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049650521, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049650546, "dur": 187, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049650736, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049650756, "dur": 1147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049651906, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049651935, "dur": 1802, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049653741, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049653775, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049653809, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049653827, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049653872, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049653885, "dur": 203, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654089, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654103, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654104, "dur": 231, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654337, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654360, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654391, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654436, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654462, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654532, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654555, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654608, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654640, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654657, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654684, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654705, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654735, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049654755, "dur": 271, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655031, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655034, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655069, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655311, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655353, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655382, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655427, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655465, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655501, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655530, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655551, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655581, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655602, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655624, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655659, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655703, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655705, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655740, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655788, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655818, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655848, "dur": 28, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655877, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655901, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655948, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655980, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049655982, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656042, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656060, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656110, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656131, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656147, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656181, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656199, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656292, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656310, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656337, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656436, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656468, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656470, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656485, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656514, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656536, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656553, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656615, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656634, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656707, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656724, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656743, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656763, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656783, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656800, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656856, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656883, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656902, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656929, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656945, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656959, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049656969, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657010, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657011, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657062, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657102, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657126, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657167, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657206, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657302, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657339, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657364, "dur": 552, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657919, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657924, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657977, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049657982, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658030, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658048, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658066, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658116, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658133, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658169, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658196, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658235, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658263, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658345, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658378, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658380, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658444, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658475, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658576, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658604, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658637, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658850, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658916, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049658917, "dur": 154, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659077, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659123, "dur": 524, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659654, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659711, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659713, "dur": 187, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659903, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659907, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049659955, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660059, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660109, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660143, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660225, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660246, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660263, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660269, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660344, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660363, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660555, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660574, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660711, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049660736, "dur": 463, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049661202, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049661228, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049661231, "dur": 59486, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049720729, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049720740, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049720865, "dur": 2415, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049723286, "dur": 4986, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049728278, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049728282, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049728308, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049728310, "dur": 684, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729004, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729048, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729051, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729173, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729259, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729264, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729325, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729421, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729423, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729479, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729500, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729525, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729606, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729633, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729635, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729776, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729797, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729845, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729899, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729950, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049729969, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730062, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730090, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730164, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730199, "dur": 448, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730649, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730677, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730708, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730743, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730767, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730823, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730865, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730867, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049730916, "dur": 992, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049731913, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049731939, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732032, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732053, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732162, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732208, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732248, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732295, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732336, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732368, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732407, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732467, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732507, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732557, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732561, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732621, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732667, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732672, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732703, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732761, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732781, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732798, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732831, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732884, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732887, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732924, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049732945, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733008, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733040, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733064, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733127, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733159, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733217, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733250, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733525, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733582, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733743, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733801, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049733842, "dur": 1413, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049735259, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049735299, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049735323, "dur": 346, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049735673, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049735709, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049735851, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049735876, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049735903, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736041, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736068, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736103, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736130, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736186, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736188, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736209, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736251, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736292, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736320, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736350, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736403, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736430, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736474, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736503, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736783, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736820, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736886, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736948, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049736950, "dur": 84, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737038, "dur": 164, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737206, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737209, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737299, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737360, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737421, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737491, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737495, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737568, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737570, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737636, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737639, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049737685, "dur": 85390, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049823085, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049823093, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049823129, "dur": 49, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049823180, "dur": 34347, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049857539, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049857547, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049857648, "dur": 10, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049857660, "dur": 91850, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049949518, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049949523, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049949556, "dur": 26, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049949584, "dur": 8678, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049958270, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049958274, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049958302, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049958306, "dur": 2941, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049961252, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049961255, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049961305, "dur": 43, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130049961349, "dur": 49448, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050010808, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050010812, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050010849, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050010852, "dur": 705, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050011562, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050011565, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050011622, "dur": 26, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050011650, "dur": 1367, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050013023, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050013031, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050013146, "dur": 1081, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754130050014230, "dur": 10625, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 30272, "tid": 690, "ts": 1754130050041596, "dur": 2242, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 30272, "tid": 8589934592, "ts": 1754130049616325, "dur": 50873, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754130049667199, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754130049667201, "dur": 1038, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 30272, "tid": 690, "ts": 1754130050043840, "dur": 24, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 30272, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130049603312, "dur": 422722, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130049605812, "dur": 5950, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130050026094, "dur": 6994, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130050030094, "dur": 80, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754130050033173, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 30272, "tid": 690, "ts": 1754130050043872, "dur": 25, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754130049624567, "dur": 1321, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130049625897, "dur": 695, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130049626706, "dur": 57, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754130049626763, "dur": 677, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130049627834, "dur": 195, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130049628916, "dur": 1043, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_38E656AD7AB33EA4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130049630121, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130049630405, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130049630561, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754130049627464, "dur": 15433, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130049642908, "dur": 369446, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130050012356, "dur": 545, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130050013080, "dur": 60, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130050013508, "dur": 144, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130050013726, "dur": 1914, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754130049627458, "dur": 15475, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049643094, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049643196, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049643468, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754130049643585, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049643729, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754130049644047, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754130049644370, "dur": 495, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754130049645089, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754130049645140, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754130049645257, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049646136, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049646776, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049647433, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049648310, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Inspector\\PropertyDrawers\\ShaderInputPropertyDrawer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754130049647970, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049649343, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049649894, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049650753, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049651282, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049651804, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049652652, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049653373, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049654090, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049654719, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049654954, "dur": 1118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049656073, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049656188, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049656298, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049656423, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049656684, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049657521, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049657841, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049658756, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049659680, "dur": 65193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049724889, "dur": 7704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754130049732672, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049732773, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754130049732894, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049733057, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049733212, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049733688, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754130049733890, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049733954, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754130049734072, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049734244, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049734556, "dur": 2264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049736821, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754130049737065, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754130049738209, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754130049738302, "dur": 274095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049627567, "dur": 15421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049643000, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754130049643403, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CC2D34224542963B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754130049643511, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049643702, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754130049643916, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754130049644064, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130049644168, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130049644288, "dur": 482, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130049644833, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130049645083, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754130049645171, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049645877, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049646431, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049647065, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049647650, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049648215, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049649327, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049649860, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049650422, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049651642, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@2.1.4\\Unity.Collections\\NativeParallelHashSetExtensions.gen.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754130049651028, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049652206, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049652699, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049653387, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049654112, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049654674, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049654948, "dur": 1110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049656059, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049656168, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049656327, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049656434, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049656655, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049656732, "dur": 1112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049657844, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049658763, "dur": 937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049659700, "dur": 65610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049725313, "dur": 8810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754130049734126, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049734490, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049734548, "dur": 1956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049736514, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049736636, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049737086, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049737138, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754130049737218, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754130049738319, "dur": 274086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049627674, "dur": 15363, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049643192, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_27474F036FBEA425.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754130049643293, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754130049643434, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049643537, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049643723, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754130049643908, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754130049644179, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130049644324, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130049644451, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130049644539, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754130049644760, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754130049644994, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130049645095, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754130049645244, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049645310, "dur": 763, "ph": "X", "name": "File", "args": {"detail": "D:\\UNRTIY\\<PERSON><PERSON>\\2022.3.61t2\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754130049645310, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049646797, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049647300, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049649160, "dur": 678, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Texture\\CalculateLevelOfDetailTexture2DNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754130049648687, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049649875, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049650589, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049651286, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049651481, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049652011, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049652386, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049653439, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049654140, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049654583, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049654910, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754130049655085, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754130049656427, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754130049656589, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754130049657197, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049657497, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049657827, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049658765, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049659656, "dur": 65188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049724863, "dur": 4874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754130049729737, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049729895, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754130049730021, "dur": 7577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754130049737602, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754130049737794, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754130049737968, "dur": 274404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049628462, "dur": 14917, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049643425, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130049643493, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130049643690, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130049643806, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130049643982, "dur": 349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A6E1C780C67641AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754130049644376, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130049644643, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130049644757, "dur": 403, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754130049645161, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049645948, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049646814, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049648054, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Importers\\ShaderGraphImporter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754130049647618, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049648808, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049649482, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049650017, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049650424, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049651180, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049651958, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049652287, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049653337, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049654130, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049654593, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049654945, "dur": 1115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049656060, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049656279, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049656417, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049656689, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049657804, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049658758, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049659681, "dur": 65221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049724906, "dur": 3865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754130049728773, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049729003, "dur": 6815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754130049735820, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049735992, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049736056, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049736991, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754130049737714, "dur": 274638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049627546, "dur": 15425, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049642982, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130049643086, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130049643171, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_78AB31D22221CD7D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130049643406, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_96EF797E350252A7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130049643493, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_96EF797E350252A7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130049643581, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130049643904, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754130049644094, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130049644257, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130049644574, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754130049644989, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130049645161, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754130049645346, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049646685, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049647743, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049648799, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049649670, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049650322, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049650891, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049651889, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049652567, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049653571, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049653709, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049653821, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049653925, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049654139, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049654600, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049654942, "dur": 1115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049656058, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049656167, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049656246, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049656404, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049656682, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754130049656878, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754130049657373, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049657540, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049657843, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049658757, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049659655, "dur": 10850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049670506, "dur": 54358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049724870, "dur": 5249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754130049730120, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049730267, "dur": 7289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754130049737558, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049737650, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754130049737700, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754130049737796, "dur": 274570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049627617, "dur": 15383, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049643322, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17CAD97A34817613.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130049643511, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049643749, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049643913, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049644013, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754130049644070, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049644150, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049644240, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049644451, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754130049644555, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754130049644717, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049644928, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049645021, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049645084, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049645150, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754130049645263, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049645914, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049646549, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049647059, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049647571, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049649505, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049650035, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049650681, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049651210, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049652267, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049652995, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049653414, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049654180, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049654555, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049654925, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130049655492, "dur": 1645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754130049657137, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049657237, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754130049657360, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754130049657808, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049658729, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049659656, "dur": 65183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049724844, "dur": 6413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754130049731259, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049731497, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049731602, "dur": 1288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049732904, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049733032, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049733309, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049733406, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049733668, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049734211, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754130049734448, "dur": 1581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049736059, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049736953, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049737735, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754130049737805, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754130049737970, "dur": 274433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049627664, "dur": 15353, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049643035, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41F9674DB235B210.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130049643187, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130049643305, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130049643517, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130049643725, "dur": 454, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130049644187, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130049644313, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130049644457, "dur": 5284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754130049649742, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049649873, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049650585, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049651051, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049652044, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049652705, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049653192, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049653321, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049654111, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049654608, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049654943, "dur": 1113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049656081, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049656206, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049656260, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049656401, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754130049656595, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049656725, "dur": 1094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049657820, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049658739, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049659670, "dur": 65225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049724896, "dur": 5872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754130049730769, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049730885, "dur": 7195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754130049738082, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754130049738207, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754130049738311, "dur": 274081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049627735, "dur": 15327, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049643076, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9EEBEC0FC3FAC728.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754130049643190, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049643511, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049643727, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_AF431D755E5610CB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754130049644035, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130049644442, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130049644547, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754130049644831, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754130049645088, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130049645147, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754130049645249, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049646068, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049646850, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049647307, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049648878, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049649285, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049650078, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049650627, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049651399, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049652137, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049652904, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049653539, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049653960, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049654543, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049654932, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754130049655278, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049655363, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754130049656120, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049656275, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049656419, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049656696, "dur": 1097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049657794, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754130049657918, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754130049658593, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049658884, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049659666, "dur": 65186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049724854, "dur": 5614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754130049730522, "dur": 7680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754130049738205, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754130049738286, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754130049738401, "dur": 274017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049627802, "dur": 15277, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049643093, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_A8FE29C950FB2CD8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049643292, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049643355, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049643443, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049643520, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049643707, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049644146, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130049644251, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130049644321, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130049644502, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754130049644654, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130049644745, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754130049644879, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130049644959, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16800515999216397072.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130049645111, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16800515999216397072.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754130049645299, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049645973, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049646880, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049647542, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049648130, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\UV\\TriplanarNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754130049648130, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049649408, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049650215, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049651022, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049651673, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049652399, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049652920, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049653534, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049654107, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049654691, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049654940, "dur": 1122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049656062, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049656166, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049656270, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049656414, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049656684, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049656892, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754130049657863, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049658063, "dur": 1808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754130049659927, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049660028, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754130049660995, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754130049661094, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754130049661462, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754130049661951, "dur": 161841, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754130049826118, "dur": 28383, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754130049826108, "dur": 30476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754130049858002, "dur": 174, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754130049858249, "dur": 91973, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754130049958651, "dur": 52805, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754130049958628, "dur": 52830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754130050011484, "dur": 774, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754130049627843, "dur": 15271, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049643183, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049643306, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049643428, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F073B613EC8B985D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754130049643499, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049643837, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754130049644045, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754130049644179, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754130049644373, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754130049644582, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130049644654, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754130049644840, "dur": 424, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754130049645265, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049645919, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049646586, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049647098, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049648077, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049649112, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049649569, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049650127, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049650753, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049651749, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049652214, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049652708, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049653187, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049653274, "dur": 685, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754130049653265, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049654200, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049654544, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049654937, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754130049655198, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049656064, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049656165, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049656248, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049656402, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049656651, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049656737, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049657810, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049658736, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049659651, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754130049660070, "dur": 64807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049724914, "dur": 5831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Export.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754130049730746, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049730813, "dur": 7197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754130049738012, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754130049738148, "dur": 274277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049627907, "dur": 15228, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049643150, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1A0DCA6DE3746D0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130049643290, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130049643435, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130049643503, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130049643700, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130049644255, "dur": 392, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754130049644720, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754130049644875, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754130049645238, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049645902, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049646420, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049647197, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049647590, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049648179, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049649234, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049649781, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049650722, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049651615, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049652205, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049652640, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049653051, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049653619, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049654018, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049654541, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049654913, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130049655076, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049655161, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754130049655695, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049655767, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049656166, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049656265, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049656402, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130049656557, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049656720, "dur": 1086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049657807, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049658717, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049658975, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754130049659094, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049659667, "dur": 65191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049724885, "dur": 4781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Documentation.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754130049729668, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049729736, "dur": 7575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754130049737314, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049737709, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754130049738302, "dur": 274082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049627949, "dur": 15206, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049643168, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_18D02D41D4689F77.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754130049643289, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754130049643511, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049643787, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754130049644110, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754130049644300, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754130049644562, "dur": 491, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754130049645053, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754130049645178, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049645269, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049646118, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049646844, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049647641, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049648700, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049649610, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049650205, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049650795, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049651701, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049652382, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049653929, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049654021, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049654565, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049654931, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754130049655328, "dur": 962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754130049656352, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754130049656537, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754130049657186, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049657826, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049658747, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049659674, "dur": 65188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049724864, "dur": 4945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754130049729810, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049729881, "dur": 6999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754130049736930, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049737000, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049737691, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754130049737817, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754130049737984, "dur": 274390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049627979, "dur": 15199, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049643189, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_3728351D5FA9AF42.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130049643383, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130049643539, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049643923, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754130049644133, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754130049644619, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130049644891, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130049645066, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130049645135, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130049645207, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049645263, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754130049645326, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049646229, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049646830, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049647323, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049648189, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049649288, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049649786, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049650805, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049651848, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049652406, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049653294, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049654079, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049654707, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049654912, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130049655121, "dur": 1886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754130049657067, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130049657261, "dur": 1451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754130049658713, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049658803, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754130049658928, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754130049659377, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049659583, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049659671, "dur": 65184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049724857, "dur": 5180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754130049730086, "dur": 6567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754130049736655, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049736844, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049737623, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754130049738337, "dur": 274056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049628015, "dur": 15178, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049643206, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130049643325, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130049643378, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_075C24B5F02EF89C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130049643435, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049643508, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_075C24B5F02EF89C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130049643563, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130049643767, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754130049643860, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754130049643927, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130049644033, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754130049644265, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130049644481, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754130049644603, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130049644844, "dur": 367, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754130049645212, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049645975, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049646653, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049647411, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049648279, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Controls\\CubemapControl.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754130049648028, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049649608, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Time\\Timer.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754130049649588, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049650811, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049651481, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049652713, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049654107, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049654681, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049654933, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754130049655499, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754130049656429, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049656651, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049656743, "dur": 1084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049657828, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049658750, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049659676, "dur": 69153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049728835, "dur": 8157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754130049736993, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049737081, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049737155, "dur": 1027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754130049738183, "dur": 274196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049628067, "dur": 15150, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049643233, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130049643323, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130049643426, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130049643506, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130049643568, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130049643786, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130049644183, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130049644332, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754130049644618, "dur": 389, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130049645008, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130049645141, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754130049645252, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049645944, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049646423, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049647269, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049647839, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049648800, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049649633, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049650359, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049651154, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049651707, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049652627, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049653065, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049653524, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049654029, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049654734, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049654943, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754130049655498, "dur": 830, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049656336, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754130049656803, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049657817, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049658733, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049659652, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754130049660221, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754130049660642, "dur": 64266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049724913, "dur": 4800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754130049729714, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049729888, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754130049729985, "dur": 7552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754130049737540, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754130049737732, "dur": 274617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049628106, "dur": 15140, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049643256, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049643323, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049643473, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049643867, "dur": 397, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130049644329, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130049644659, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130049644720, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130049644938, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9630950048426576611.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130049645058, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754130049645153, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049645865, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049646461, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049647026, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049647687, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049648714, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049649894, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049650694, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049651431, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049651849, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049652489, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049653483, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049654149, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049654576, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049654935, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049655267, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049655368, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130049656285, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049656426, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049656684, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049657071, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049657302, "dur": 1847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130049659202, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049659319, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130049660148, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049660256, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130049660870, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049660978, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754130049661319, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754130049661439, "dur": 63444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049724888, "dur": 4797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754130049729686, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049729896, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754130049730007, "dur": 7664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754130049737673, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754130049737801, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754130049737944, "dur": 274419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049628156, "dur": 15112, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049643283, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B4D1B567B4047FDB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130049643507, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130049643572, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130049643793, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130049644134, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1754130049644233, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130049644371, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1754130049644610, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130049644761, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130049644961, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130049645099, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754130049645238, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049645309, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049646146, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049647075, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049647818, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049648705, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049649418, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049650034, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049650545, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049651670, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049652165, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049652997, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049653566, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049653873, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049653943, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049654020, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049654542, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049654917, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754130049655307, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754130049656457, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049656709, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049657796, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049658719, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049659663, "dur": 65274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049724939, "dur": 5350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754130049730343, "dur": 6537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754130049736948, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1754130049737047, "dur": 884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049737936, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754130049738028, "dur": 274354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049628187, "dur": 15100, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049643427, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130049643527, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130049643726, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754130049643825, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754130049643924, "dur": 548, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754130049644507, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1754130049644748, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1754130049644846, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1754130049645178, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049645905, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049646935, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\AddDelete\\AddDeleteItemModeMix.cs"}}, {"pid": 12345, "tid": 18, "ts": 1754130049646467, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049647597, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049648168, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049649041, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049649814, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049650532, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049651105, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049651520, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049652035, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049652873, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049653542, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049653917, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049654066, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049654726, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049654929, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754130049655395, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1754130049656191, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049656289, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049656421, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049656714, "dur": 1091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049657806, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049658756, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049659660, "dur": 65233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049724902, "dur": 5144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754130049730048, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049730148, "dur": 7382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754130049737534, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754130049737736, "dur": 274610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049628222, "dur": 15081, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049643378, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754130049643494, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049643685, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754130049644021, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1754130049644133, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1754130049644406, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130049644541, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1754130049644693, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130049644762, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130049644969, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130049645076, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754130049645201, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049646201, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049646751, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.35\\Rider\\Editor\\UnitTesting\\SyncTestRunCallback.cs"}}, {"pid": 12345, "tid": 19, "ts": 1754130049646751, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049647989, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049649274, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049649977, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049650672, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049651754, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049652410, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049652972, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049653428, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049653891, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049653989, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049654562, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049654924, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754130049655434, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754130049656467, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049656687, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754130049656933, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754130049657937, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049658060, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049658728, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049659654, "dur": 9312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049668967, "dur": 1533, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049670500, "dur": 54664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049725166, "dur": 5220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dots.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754130049730387, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049730693, "dur": 7420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754130049738116, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754130049738190, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754130049738320, "dur": 274091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049628264, "dur": 15057, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049643409, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130049643500, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049643704, "dur": 383, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130049644092, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130049644209, "dur": 6916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130049651126, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049651270, "dur": 1350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130049652643, "dur": 1814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130049654558, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130049654624, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130049654936, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130049655287, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130049656136, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049656324, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049656658, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130049656887, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130049657793, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754130049657930, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754130049658540, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049658761, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 20, "ts": 1754130049659365, "dur": 88, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049659836, "dur": 61576, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 20, "ts": 1754130049724878, "dur": 3877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Newtonsoft.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754130049728760, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049729026, "dur": 7277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754130049736303, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049736429, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049736586, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049736953, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1754130049737042, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754130049737818, "dur": 274550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049628307, "dur": 15027, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049643420, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1983ABD9EB6C4B5C.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130049643557, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049643701, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130049644019, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1754130049644141, "dur": 477, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1754130049644651, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130049644759, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1754130049645108, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754130049645226, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049646036, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049646749, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049647738, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049648879, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049649362, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049649936, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049650730, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049651600, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049652194, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049653177, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049653258, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049653766, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049653931, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049654064, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049654712, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049654916, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130049655284, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754130049656021, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049656173, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049656338, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049656408, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049656646, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130049656886, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754130049657704, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049657847, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049658761, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049659653, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049660153, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754130049660300, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754130049660831, "dur": 64256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049725088, "dur": 5422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754130049730511, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049730597, "dur": 7603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754130049738203, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754130049738328, "dur": 274071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049628349, "dur": 15003, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049643426, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130049643511, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130049643699, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1754130049643862, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130049644020, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130049644136, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1754130049644407, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130049644508, "dur": 413, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130049644922, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3676569134375922281.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130049645011, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754130049645180, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049645276, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049646337, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049646892, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049647443, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049648086, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049648795, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049649265, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049649793, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049650545, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049651583, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049652108, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049652718, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049653350, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049654162, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049654567, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049654938, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130049655371, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754130049656091, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049656183, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049656319, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049656437, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049656702, "dur": 1092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049657795, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754130049657914, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049658096, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049658767, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049659658, "dur": 65177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049724837, "dur": 5098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754130049729936, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049730057, "dur": 6627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754130049736687, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049736867, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049736937, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049737648, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049737713, "dur": 220930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754130049958716, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754130049958652, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754130049959000, "dur": 2994, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754130049962001, "dur": 50354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049627490, "dur": 15466, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049643197, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754130049643290, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049643424, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049643520, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754130049643685, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754130049643823, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130049643920, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130049644093, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130049644248, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130049644417, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130049644505, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1754130049644829, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130049645027, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4824701561327399296.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754130049645190, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049646027, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049646542, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049647209, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049647861, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049648227, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049649150, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049649874, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049650783, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049651290, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049652291, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049653069, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049653400, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049654092, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049654699, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049654949, "dur": 1107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049656083, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049656194, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049656268, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049656413, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049656645, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754130049656862, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754130049657501, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049657835, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049658752, "dur": 939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049659691, "dur": 65180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049724872, "dur": 6145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754130049731018, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049731393, "dur": 6722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754130049738120, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754130049738192, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754130049738312, "dur": 274078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049628370, "dur": 14996, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049643473, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754130049643549, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754130049643770, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130049643850, "dur": 350, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130049644250, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130049644396, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130049644482, "dur": 497, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130049644979, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754130049645198, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049646555, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049647078, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049647795, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049648277, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049649130, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049649635, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049650053, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049650862, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049651699, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049652219, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049653009, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049653605, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049653871, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049654020, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049654540, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049654911, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754130049655116, "dur": 1445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754130049656687, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754130049656941, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754130049657632, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754130049657832, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754130049658971, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754130049659082, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754130049659674, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754130049660197, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754130049660800, "dur": 64090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049724892, "dur": 5255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754130049730150, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049730240, "dur": 7117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754130049737359, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049737615, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754130049738306, "dur": 274081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754130050023708, "dur": 1325, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 30272, "tid": 690, "ts": 1754130050044585, "dur": 2028, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 30272, "tid": 690, "ts": 1754130050046654, "dur": 2221, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 30272, "tid": 690, "ts": 1754130050040220, "dur": 9558, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}