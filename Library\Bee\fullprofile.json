{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 30272, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 30272, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 30272, "tid": 387, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 30272, "tid": 387, "ts": 1754129082994824, "dur": 504, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 30272, "tid": 387, "ts": 1754129082997920, "dur": 636, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 30272, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 30272, "tid": 1, "ts": 1754129082521280, "dur": 3257, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754129082524539, "dur": 18228, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30272, "tid": 1, "ts": 1754129082542777, "dur": 24333, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 30272, "tid": 387, "ts": 1754129082998562, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 30272, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082520060, "dur": 8233, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082528295, "dur": 454748, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082528956, "dur": 2091, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082531053, "dur": 1068, "ph": "X", "name": "ProcessMessages 14408", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082532123, "dur": 806, "ph": "X", "name": "ReadAsync 14408", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082532931, "dur": 6, "ph": "X", "name": "ProcessMessages 20528", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082532939, "dur": 139, "ph": "X", "name": "ReadAsync 20528", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533084, "dur": 4, "ph": "X", "name": "ProcessMessages 4682", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533088, "dur": 34, "ph": "X", "name": "ReadAsync 4682", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533125, "dur": 43, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533172, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533174, "dur": 32, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533208, "dur": 29, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533240, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533243, "dur": 41, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533288, "dur": 33, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533324, "dur": 24, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533350, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533353, "dur": 18, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533373, "dur": 82, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533457, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533488, "dur": 28, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533519, "dur": 28, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533549, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533582, "dur": 32, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533616, "dur": 21, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533639, "dur": 45, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533687, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533706, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533731, "dur": 47, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533780, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533803, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533830, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533832, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533858, "dur": 14, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533875, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533899, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533901, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533925, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533949, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082533988, "dur": 23, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534013, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534040, "dur": 25, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534068, "dur": 27, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534097, "dur": 27, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534128, "dur": 21, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534152, "dur": 30, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534186, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534208, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534210, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534239, "dur": 23, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534265, "dur": 24, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534292, "dur": 22, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534316, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534339, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534341, "dur": 24, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534368, "dur": 15, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534385, "dur": 15, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534402, "dur": 19, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534424, "dur": 31, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534459, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534483, "dur": 21, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534506, "dur": 23, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534530, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534531, "dur": 12, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534545, "dur": 13, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534562, "dur": 14, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534578, "dur": 15, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534596, "dur": 15, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534613, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534631, "dur": 14, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534647, "dur": 25, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534674, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534690, "dur": 24, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534716, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534741, "dur": 22, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534766, "dur": 14, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534781, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534783, "dur": 16, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534801, "dur": 25, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534830, "dur": 18, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534850, "dur": 25, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534876, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534896, "dur": 17, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534914, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534933, "dur": 14, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534948, "dur": 27, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534976, "dur": 18, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082534996, "dur": 30, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535028, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535045, "dur": 29, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535079, "dur": 4, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535086, "dur": 64, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535153, "dur": 1, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535156, "dur": 37, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535194, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535197, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535222, "dur": 26, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535250, "dur": 15, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535267, "dur": 13, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535282, "dur": 32, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535317, "dur": 15, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535334, "dur": 18, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535353, "dur": 18, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535372, "dur": 32, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535406, "dur": 43, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535451, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535478, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535501, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535523, "dur": 48, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535574, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535593, "dur": 15, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535609, "dur": 18, "ph": "X", "name": "ReadAsync 14", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535629, "dur": 19, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535650, "dur": 23, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535675, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535691, "dur": 28, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535721, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535742, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535786, "dur": 24, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535814, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535817, "dur": 55, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535873, "dur": 2, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535875, "dur": 20, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535897, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535917, "dur": 14, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535933, "dur": 14, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535948, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535949, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535966, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535983, "dur": 12, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082535997, "dur": 17, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536016, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536033, "dur": 14, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536049, "dur": 13, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536065, "dur": 26, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536092, "dur": 14, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536110, "dur": 13, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536125, "dur": 12, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536139, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536171, "dur": 13, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536185, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536201, "dur": 22, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536227, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536229, "dur": 29, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536261, "dur": 16, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536280, "dur": 14, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536295, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536313, "dur": 16, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536330, "dur": 15, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536347, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536363, "dur": 13, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536377, "dur": 26, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536405, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536415, "dur": 138, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536554, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536571, "dur": 14, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536587, "dur": 12, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536601, "dur": 14, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536616, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536632, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536651, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536671, "dur": 15, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536688, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536710, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536730, "dur": 14, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536746, "dur": 13, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536761, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536778, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536791, "dur": 13, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536805, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536807, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536822, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536825, "dur": 14, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536841, "dur": 15, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536858, "dur": 13, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536873, "dur": 14, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536889, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536904, "dur": 13, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536919, "dur": 13, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536934, "dur": 11, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536948, "dur": 14, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536963, "dur": 13, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536978, "dur": 11, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082536991, "dur": 12, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537004, "dur": 17, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537023, "dur": 15, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537039, "dur": 15, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537056, "dur": 13, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537071, "dur": 15, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537087, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537104, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537115, "dur": 14, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537130, "dur": 14, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537147, "dur": 14, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537163, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537178, "dur": 9, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537189, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537214, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537234, "dur": 9, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537245, "dur": 12, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537259, "dur": 16, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537276, "dur": 13, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537292, "dur": 12, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537304, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537306, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537322, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537338, "dur": 14, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537354, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537371, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537387, "dur": 13, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537403, "dur": 12, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537417, "dur": 15, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537433, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537450, "dur": 14, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537466, "dur": 14, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537483, "dur": 14, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537497, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537499, "dur": 15, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537516, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537532, "dur": 15, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537549, "dur": 14, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537566, "dur": 13, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537582, "dur": 15, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537599, "dur": 12, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537613, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537628, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537646, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537663, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537680, "dur": 13, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537694, "dur": 14, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537710, "dur": 10, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537723, "dur": 14, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537739, "dur": 12, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537753, "dur": 8, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537763, "dur": 14, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537778, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537793, "dur": 16, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537810, "dur": 14, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537827, "dur": 16, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537845, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537861, "dur": 17, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537883, "dur": 15, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537900, "dur": 16, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537918, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537933, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537935, "dur": 16, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537952, "dur": 13, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537967, "dur": 14, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082537983, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538000, "dur": 14, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538015, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538031, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538033, "dur": 45, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538081, "dur": 26, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538109, "dur": 20, "ph": "X", "name": "ReadAsync 1239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538130, "dur": 25, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538157, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538178, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538180, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538204, "dur": 16, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538224, "dur": 25, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538253, "dur": 19, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538277, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538296, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538313, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538315, "dur": 21, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538337, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538358, "dur": 11, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538372, "dur": 18, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538393, "dur": 16, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538412, "dur": 17, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538432, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538451, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538470, "dur": 19, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538491, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538509, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538528, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538545, "dur": 18, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538566, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538584, "dur": 18, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538606, "dur": 19, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538626, "dur": 15, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538644, "dur": 28, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538674, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538692, "dur": 16, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538712, "dur": 15, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538730, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538748, "dur": 16, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538766, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538784, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538799, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538825, "dur": 14, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538841, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538860, "dur": 14, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538876, "dur": 13, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538891, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538893, "dur": 14, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538910, "dur": 14, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538926, "dur": 17, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538952, "dur": 24, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082538979, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539002, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539004, "dur": 26, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539033, "dur": 49, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539083, "dur": 39, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539125, "dur": 2, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539128, "dur": 48, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539182, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539187, "dur": 55, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539247, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539249, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539299, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539301, "dur": 33, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539337, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539340, "dur": 39, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539380, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539383, "dur": 31, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539416, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539423, "dur": 25, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539451, "dur": 29, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539486, "dur": 29, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539518, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539555, "dur": 26, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539583, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539585, "dur": 43, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539635, "dur": 24, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539661, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539664, "dur": 62, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539732, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539781, "dur": 28, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539812, "dur": 28, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539843, "dur": 22, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539868, "dur": 55, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539925, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539977, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082539979, "dur": 39, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540021, "dur": 22, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540045, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540091, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540160, "dur": 37, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540200, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540217, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540220, "dur": 133, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540356, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540379, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540383, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540409, "dur": 16, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540448, "dur": 18, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540470, "dur": 76, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540551, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540579, "dur": 19, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540601, "dur": 26, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540629, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540649, "dur": 80, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540733, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540748, "dur": 23, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540773, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540775, "dur": 26, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540803, "dur": 13, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540818, "dur": 57, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540876, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540890, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540892, "dur": 14, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540908, "dur": 12, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540921, "dur": 61, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540983, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082540999, "dur": 14, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541015, "dur": 14, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541029, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541031, "dur": 12, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541044, "dur": 47, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541095, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541115, "dur": 13, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541130, "dur": 9, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541140, "dur": 53, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541195, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541213, "dur": 15, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541231, "dur": 14, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541247, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541298, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541314, "dur": 10, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541325, "dur": 14, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541340, "dur": 11, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541353, "dur": 59, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541415, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541433, "dur": 10, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541444, "dur": 10, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541455, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541521, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541541, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541559, "dur": 11, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541572, "dur": 50, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541623, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541638, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541654, "dur": 12, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541668, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541722, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541736, "dur": 13, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541752, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541768, "dur": 13, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541783, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541800, "dur": 49, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541850, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541865, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541881, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541903, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541920, "dur": 15, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541938, "dur": 14, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541953, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541968, "dur": 12, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541982, "dur": 14, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082541998, "dur": 14, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542013, "dur": 12, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542026, "dur": 50, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542078, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542092, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542093, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542110, "dur": 12, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542125, "dur": 51, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542177, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542193, "dur": 15, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542211, "dur": 14, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542226, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542281, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542306, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542323, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542344, "dur": 55, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542402, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542423, "dur": 17, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542442, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542444, "dur": 19, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542465, "dur": 47, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542513, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542535, "dur": 20, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542556, "dur": 37, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542596, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542679, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542683, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542757, "dur": 2, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542760, "dur": 34, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542797, "dur": 20, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542818, "dur": 12, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542831, "dur": 47, "ph": "X", "name": "ReadAsync 35", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542882, "dur": 33, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082542917, "dur": 116, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543035, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543062, "dur": 20, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543083, "dur": 17, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543101, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543122, "dur": 14, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543137, "dur": 36, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543175, "dur": 3, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543179, "dur": 27, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543207, "dur": 75, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543283, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543313, "dur": 25, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543341, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543366, "dur": 14, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543381, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543404, "dur": 95, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543500, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543523, "dur": 21, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543546, "dur": 24, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543571, "dur": 24, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543597, "dur": 50, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543651, "dur": 34, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543686, "dur": 34, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543722, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543744, "dur": 90, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543835, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543857, "dur": 13, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543872, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543916, "dur": 34, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543953, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543956, "dur": 36, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082543994, "dur": 32, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544029, "dur": 52, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544083, "dur": 2, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544086, "dur": 31, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544120, "dur": 29, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544151, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544179, "dur": 29, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544211, "dur": 27, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544241, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544320, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544359, "dur": 28, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544389, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544415, "dur": 19, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544436, "dur": 63, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544501, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544531, "dur": 28, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544562, "dur": 81, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544645, "dur": 55, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544702, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544749, "dur": 2, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544752, "dur": 37, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544791, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544812, "dur": 14, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544830, "dur": 73, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544905, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544937, "dur": 16, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082544955, "dur": 44, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545000, "dur": 33, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545034, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545052, "dur": 22, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545075, "dur": 17, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545093, "dur": 109, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545204, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545229, "dur": 46, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545277, "dur": 46, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545326, "dur": 34, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545362, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545385, "dur": 61, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545448, "dur": 43, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545492, "dur": 35, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545529, "dur": 41, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545572, "dur": 16, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545590, "dur": 36, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545627, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545653, "dur": 94, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545749, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545784, "dur": 24, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545810, "dur": 16, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545828, "dur": 29, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545859, "dur": 15, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545877, "dur": 30, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545908, "dur": 19, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545929, "dur": 33, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545963, "dur": 22, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082545986, "dur": 28, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546017, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546039, "dur": 23, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546064, "dur": 48, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546116, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546137, "dur": 209, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546347, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546370, "dur": 53, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546426, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546445, "dur": 35, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546481, "dur": 30, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546514, "dur": 216, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546731, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546754, "dur": 17, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546772, "dur": 24, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546798, "dur": 14, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546814, "dur": 15, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546830, "dur": 15, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546847, "dur": 13, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546861, "dur": 18, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546880, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546882, "dur": 23, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546908, "dur": 45, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082546955, "dur": 68, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547027, "dur": 4, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547032, "dur": 69, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547105, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547107, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547168, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547279, "dur": 3, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547284, "dur": 30, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547314, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547316, "dur": 30, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547352, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547357, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547439, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547441, "dur": 42, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547486, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547488, "dur": 39, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547529, "dur": 50, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547580, "dur": 115, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547697, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547725, "dur": 35, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547762, "dur": 69, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547833, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547836, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547867, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547869, "dur": 38, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547910, "dur": 35, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547948, "dur": 3, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547952, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547975, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082547976, "dur": 28, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548007, "dur": 55, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548065, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548115, "dur": 37, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548155, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548158, "dur": 27, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548187, "dur": 23, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548213, "dur": 1, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548215, "dur": 26, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548243, "dur": 28, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548273, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548294, "dur": 18, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548315, "dur": 21, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548338, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548359, "dur": 153, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548514, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548537, "dur": 33, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548572, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548594, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548596, "dur": 28, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548628, "dur": 20, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548651, "dur": 18, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548671, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548695, "dur": 26, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548722, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548740, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548742, "dur": 34, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548777, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548801, "dur": 13, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548815, "dur": 74, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548891, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548907, "dur": 14, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548922, "dur": 14, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548938, "dur": 17, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548957, "dur": 14, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548973, "dur": 13, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082548988, "dur": 14, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549003, "dur": 13, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549018, "dur": 19, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549039, "dur": 12, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549053, "dur": 14, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549069, "dur": 8, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549078, "dur": 82, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549163, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549183, "dur": 128, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549320, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549377, "dur": 355, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549735, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549856, "dur": 4, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549862, "dur": 59, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549923, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549925, "dur": 35, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082549962, "dur": 40, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550005, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550039, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550041, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550095, "dur": 47, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550145, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550148, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550201, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550203, "dur": 57, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550262, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550264, "dur": 48, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550316, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550320, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550381, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550427, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550464, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550500, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550504, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550550, "dur": 23, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550575, "dur": 17, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550594, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550617, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550655, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550709, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550712, "dur": 57, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550771, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550774, "dur": 38, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550817, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550849, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550899, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550951, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550952, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082550976, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551001, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551023, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551043, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551065, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551085, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551106, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551130, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551179, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551180, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551239, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551241, "dur": 47, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551289, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551291, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551351, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551354, "dur": 49, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551407, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551459, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551462, "dur": 118, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551584, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551646, "dur": 25, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551673, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551674, "dur": 12, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551690, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551713, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551742, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551773, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551803, "dur": 36, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082551842, "dur": 5106, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082556952, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082556982, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082557005, "dur": 2255, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082559263, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082559301, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082559319, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082559348, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082559399, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082559428, "dur": 1393, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082560824, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082560867, "dur": 1977, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082562849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082562851, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082562903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082562905, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082562952, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563013, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563015, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563034, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563295, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563324, "dur": 292, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563619, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563647, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563669, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563685, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563717, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563739, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563803, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563830, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563835, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563865, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563888, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563912, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563979, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082563981, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564028, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564066, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564096, "dur": 436, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564536, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564575, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564620, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564622, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564645, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564683, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564714, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564746, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564787, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564789, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564821, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564860, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564890, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564936, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082564963, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565002, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565038, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565040, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565084, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565115, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565142, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565144, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565177, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565179, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565210, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565226, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565246, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565268, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565285, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565287, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565329, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565350, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565417, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565435, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565451, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565517, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565534, "dur": 325, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565865, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565924, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565926, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565981, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082565983, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566108, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566129, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566161, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566162, "dur": 20, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566185, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566202, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566230, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566233, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566257, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566259, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566298, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566441, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566471, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566493, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566599, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566628, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566650, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566669, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566718, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566733, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566748, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566762, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566889, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566907, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566928, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082566972, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567003, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567021, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567089, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567111, "dur": 412, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567525, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567559, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567650, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567709, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567778, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567826, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567854, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567895, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082567915, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568124, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568157, "dur": 391, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568550, "dur": 38, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568592, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568673, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568712, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568716, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568752, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568771, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568838, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568859, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082568881, "dur": 320, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569204, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569233, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569340, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569366, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569394, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569421, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569486, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569517, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569553, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569583, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569650, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569690, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569692, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569760, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569790, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569867, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082569891, "dur": 499, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082570392, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082570428, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082570431, "dur": 49148, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082619584, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082619588, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082619630, "dur": 1428, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082621060, "dur": 4945, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082626010, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082626053, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082626057, "dur": 913, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082626974, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627030, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627039, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627194, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627253, "dur": 212, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627469, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627509, "dur": 209, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627720, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627749, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627785, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627800, "dur": 183, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082627987, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628015, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628046, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628145, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628192, "dur": 278, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628473, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628514, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628688, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628716, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628922, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082628949, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082629170, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082629199, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082629536, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082629569, "dur": 2164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082631737, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082631808, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082631813, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082631866, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082631894, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082631911, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082631960, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082631999, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632027, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632055, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632110, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632116, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632213, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632313, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632319, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632378, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632380, "dur": 78, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632462, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632524, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632575, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632604, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632624, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632701, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632739, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632810, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632813, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632890, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632893, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632945, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082632962, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633034, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633074, "dur": 174, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633256, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633319, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633323, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633390, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633436, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633439, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633482, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082633521, "dur": 520, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634045, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634085, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634101, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634123, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634142, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634195, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634224, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634285, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634288, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634353, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634356, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634425, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634433, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634494, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634530, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634532, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634561, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634562, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634592, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634612, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634645, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634647, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634817, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634819, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634856, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082634986, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635038, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635041, "dur": 86, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635131, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635202, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635205, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635246, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635283, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635286, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635342, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635345, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635393, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635396, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082635437, "dur": 96387, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082731829, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082731834, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082731906, "dur": 23, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082731930, "dur": 31889, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082763829, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082763834, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082763868, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082763871, "dur": 145529, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082909414, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082909420, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082909497, "dur": 28, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082909526, "dur": 7458, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082916994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082916997, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082917033, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082917036, "dur": 2605, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082919651, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082919660, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082919724, "dur": 90, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082919817, "dur": 49873, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082969702, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082969706, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082969768, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082969771, "dur": 750, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082970528, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082970532, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082970612, "dur": 49, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082970663, "dur": 1374, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082972044, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082972051, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082972117, "dur": 735, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 30272, "tid": 12884901888, "ts": 1754129082972855, "dur": 10141, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 30272, "tid": 387, "ts": 1754129082998582, "dur": 1381, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129082518385, "dur": 49208, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129082567598, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 30272, "tid": 8589934592, "ts": 1754129082567602, "dur": 1197, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 30272, "tid": 387, "ts": 1754129082999964, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 30272, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129082506120, "dur": 478491, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129082509041, "dur": 5547, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129082984701, "dur": 5962, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129082987805, "dur": 58, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 30272, "tid": 4294967296, "ts": 1754129082990853, "dur": 48, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 30272, "tid": 387, "ts": 1754129082999974, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754129082526590, "dur": 64, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082526684, "dur": 1515, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082528211, "dur": 664, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082529030, "dur": 690, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082530397, "dur": 116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129082531323, "dur": 1145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129082532841, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129082533121, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129082535181, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129082535458, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129082539035, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129082539654, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129082542671, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129082543475, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754129082544794, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754129082544883, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129082545322, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754129082545375, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754129082545667, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754129082546277, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129082547097, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Newtonsoft.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754129082547198, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754129082529743, "dur": 19323, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082549078, "dur": 421454, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082970536, "dur": 660, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082971245, "dur": 99, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082971648, "dur": 65, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082971753, "dur": 93, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082971900, "dur": 2257, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754129082529516, "dur": 19582, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082549335, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_78AB31D22221CD7D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129082549547, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129082549706, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129082549819, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129082549932, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129082550295, "dur": 379, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754129082550677, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129082550935, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129082551110, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754129082551263, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754129082551531, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082552407, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082552935, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082553658, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082554163, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082554930, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082555731, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082556718, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082557371, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082558100, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082558547, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082559834, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082560559, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082561674, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082562129, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082562824, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082563281, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129082563899, "dur": 1207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754129082565107, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082565225, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754129082565414, "dur": 1170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754129082566656, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082566851, "dur": 1328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082568180, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082568672, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754129082569045, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082569100, "dur": 52839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082621953, "dur": 6451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754129082628406, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082628574, "dur": 5854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754129082634429, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754129082634514, "dur": 336060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082529621, "dur": 19527, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082549161, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129082549320, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082549383, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129082549476, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129082549581, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082549750, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129082549970, "dur": 450, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129082550448, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129082550557, "dur": 6189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754129082556747, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082556906, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082557662, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082558403, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082559709, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082560512, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082561758, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082561961, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082562173, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082562797, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082563319, "dur": 1204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082564552, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082564796, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082564879, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082564959, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754129082565112, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082565218, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754129082566033, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082566237, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082566867, "dur": 1319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082568186, "dur": 53697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082621885, "dur": 7405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754129082629296, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082629428, "dur": 5584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754129082635015, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754129082635132, "dur": 335484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082530312, "dur": 19160, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082549491, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129082549728, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129082549826, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129082549943, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129082550056, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129082550152, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129082550456, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129082550521, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129082550910, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129082551026, "dur": 395, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754129082551423, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754129082551560, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082551712, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082552621, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082553130, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082554013, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082554938, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082556406, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082557164, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082557844, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082559356, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorRangeAttribute.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754129082558805, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082560294, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082561403, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082562138, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082562798, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082563285, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129082563786, "dur": 1873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754129082565660, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082565767, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754129082566036, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754129082566560, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082566896, "dur": 1291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082568187, "dur": 54236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082622426, "dur": 9142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dots.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754129082631570, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082631698, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082631878, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082632041, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082632265, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082632423, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082632512, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082632714, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082633175, "dur": 857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082634072, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754129082634725, "dur": 335898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082530083, "dur": 19266, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082549357, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129082549456, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129082549581, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082549659, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129082549781, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129082549924, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129082550249, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754129082550402, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129082550528, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129082550743, "dur": 469, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129082551213, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3676569134375922281.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129082551327, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3676569134375922281.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754129082551549, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082552151, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082552862, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082553368, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082554099, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082554896, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082555808, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082556508, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082557656, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082558451, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082558977, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082559725, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082560477, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082561716, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082561993, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082562133, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082562815, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082563296, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754129082563763, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754129082564907, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082565040, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082565849, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082566203, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082566845, "dur": 1336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082568181, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082568695, "dur": 1883, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082570578, "dur": 51342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082621921, "dur": 6991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754129082628914, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082629068, "dur": 5963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754129082635033, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754129082635138, "dur": 335414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082529613, "dur": 19521, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082549143, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082549356, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082549429, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_F5A419B9F1F8246D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082549567, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082549702, "dur": 727, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082550437, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082550534, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082550717, "dur": 8277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129082558995, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082559187, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082560449, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082560690, "dur": 1999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129082562809, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082562894, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129082563263, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082563713, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129082564979, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082565184, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082565344, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129082566219, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754129082566348, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754129082566870, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1754129082567539, "dur": 190, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082568014, "dur": 51441, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1754129082621882, "dur": 5880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Newtonsoft.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754129082627763, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082628009, "dur": 6282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754129082634293, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754129082634417, "dur": 336126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082529661, "dur": 19522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082549201, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129082549337, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082549419, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082549551, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082549647, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082549707, "dur": 501, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129082550242, "dur": 389, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_BE343FF0452B4331.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129082550633, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129082550741, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129082551175, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129082551281, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129082551391, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129082551489, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754129082551642, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082552745, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082553265, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082553745, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082554182, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082555572, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\VirtualTextureShaderProperty.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754129082555472, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082556800, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082557658, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082558520, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082559081, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082560086, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082561301, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082562186, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082562801, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082563306, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754129082563766, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082564556, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082564804, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082564898, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082565057, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082565868, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082566219, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082566863, "dur": 1340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082568204, "dur": 53748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082621970, "dur": 5147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754129082627119, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082627363, "dur": 6986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754129082634350, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754129082634470, "dur": 336152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082529744, "dur": 19476, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082549233, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082549360, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082549515, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082549658, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082549783, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082549902, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082550004, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082550096, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129082550233, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_965F5DAE5AE88371.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082550315, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129082550412, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129082550639, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129082550950, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129082551092, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129082551234, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129082551410, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754129082551603, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082551698, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082552791, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082553697, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082554885, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Generation\\Data\\ConditionalField.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754129082554248, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082555669, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082556626, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082557312, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082558178, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082559038, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082559887, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082561009, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082561419, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082562228, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082562814, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082563288, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754129082563864, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754129082564708, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082564804, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082564892, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082564972, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082565050, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082565855, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082566212, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082566857, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082568176, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754129082568576, "dur": 53385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082621965, "dur": 6679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754129082628646, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754129082628819, "dur": 6120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754129082635028, "dur": 335511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082529776, "dur": 19463, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082549252, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9EEBEC0FC3FAC728.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129082549307, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082549390, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129082549532, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129082549648, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082549705, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129082549920, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082550018, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129082550388, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754129082550553, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129082550627, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129082550934, "dur": 383, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129082551320, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129082551527, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754129082551654, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082552976, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082553544, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082554367, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082555416, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082556296, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082557436, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082558334, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082559376, "dur": 1422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082560799, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082561450, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082562192, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082562810, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082563286, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754129082563790, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754129082564788, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082565027, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082565841, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082566213, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082566855, "dur": 1322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082568178, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754129082568633, "dur": 53299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082621935, "dur": 5487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754129082627424, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082627606, "dur": 6627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754129082634234, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082634332, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754129082635309, "dur": 335269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082529831, "dur": 19421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082549294, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082549354, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_27474F036FBEA425.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129082549532, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082549753, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8444FDB0B5BEDD1D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129082549893, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129082550058, "dur": 555, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754129082550694, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754129082550924, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129082551167, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129082551253, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16800515999216397072.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129082551408, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754129082551549, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082552208, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082553216, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082553686, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082554262, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082554937, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Texture\\TextureStackNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754129082554937, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082556320, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082557213, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082558310, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082558812, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082559865, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082560695, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082562109, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082562812, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082563321, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082564527, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082564784, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082564865, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082564997, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082565826, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082566188, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754129082566329, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082566871, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082568188, "dur": 53715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082621908, "dur": 4984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754129082626896, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082627072, "dur": 7401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754129082634474, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754129082634524, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754129082634719, "dur": 335840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082529863, "dur": 19412, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082549357, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082549453, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082549583, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082549779, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082550011, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082550280, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754129082550416, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129082550574, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754129082550634, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129082550830, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754129082551055, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754129082551205, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129082551395, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754129082551651, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082552754, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082553670, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082554447, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082555325, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082556044, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082556702, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082557400, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082558068, "dur": 2094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082560163, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082561744, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\ITestRunSettings.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754129082561400, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082562359, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082562829, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082563285, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082563511, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082564331, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082564471, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082564536, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082564799, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082564883, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082564958, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082565131, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082565801, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082566048, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082566226, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082566370, "dur": 1179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082567612, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082567666, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082567785, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082568601, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082568749, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082569285, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082569371, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082569664, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754129082569759, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082570299, "dur": 161409, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082733029, "dur": 27728, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754129082733021, "dur": 29026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082763494, "dur": 147, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754129082763710, "dur": 145519, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754129082916627, "dur": 52854, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754129082916617, "dur": 52868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754129082969509, "dur": 885, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754129082529917, "dur": 19375, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082549350, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082549472, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082549632, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129082549695, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754129082549850, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082549967, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082550185, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082550350, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082550630, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082550721, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082550860, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082551053, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082551159, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082551358, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082551467, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754129082551568, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082551676, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082552852, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082553638, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082554230, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082555278, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082556018, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082556908, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082557440, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082558211, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082559098, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082559796, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082560865, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082560960, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082561369, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082562159, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082562789, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082563322, "dur": 1204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082564526, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082564785, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082564915, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082565033, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082565851, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082566206, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082566864, "dur": 1335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082568200, "dur": 53745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082621962, "dur": 5861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754129082627825, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082627914, "dur": 5883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754129082633799, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082633970, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754129082634519, "dur": 336072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082529960, "dur": 19348, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082549321, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_18D02D41D4689F77.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129082549522, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129082549650, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082549710, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129082549803, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129082549915, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129082550138, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754129082550232, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A6E1C780C67641AE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754129082550399, "dur": 306, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754129082550800, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754129082550949, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129082551082, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129082551164, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129082551372, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4824701561327399296.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754129082551605, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082551671, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082552927, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082553339, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082554152, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082554775, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082555719, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082556764, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082557715, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082558496, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082559358, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082560720, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082561697, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082562049, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082562131, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082562811, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082563316, "dur": 1218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082564534, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082564799, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082564890, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082564968, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082565064, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082565861, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082566211, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082566868, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082568197, "dur": 57589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082625791, "dur": 5688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754129082631481, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082631633, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754129082631798, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082631978, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082632206, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082632344, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082632474, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082632654, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082632931, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082633951, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754129082634482, "dur": 336124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082529994, "dur": 19332, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082549334, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_66FDAEB71878BAE6.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129082549471, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082549667, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1983ABD9EB6C4B5C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129082549851, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082549922, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082550059, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754129082550442, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129082550948, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129082551043, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129082551206, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129082551348, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754129082551589, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082552896, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082553659, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082554629, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082556453, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082557453, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082558405, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082559224, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082560257, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082560928, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082561140, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082562329, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082562825, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082563323, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082564528, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082564778, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754129082565170, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082565828, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082566229, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082566861, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082568199, "dur": 53760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082621962, "dur": 5087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754129082627051, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754129082627333, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754129082627390, "dur": 7377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754129082634848, "dur": 335747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082530039, "dur": 19296, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082549344, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EA8F9A3BC19611D7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129082549542, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_075C24B5F02EF89C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129082549659, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_075C24B5F02EF89C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129082549807, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129082549905, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082550014, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129082550128, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754129082550737, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754129082551170, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129082551250, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10131660672807692368.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129082551324, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129082551460, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754129082551541, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082552290, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082552941, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082553557, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082554777, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082555934, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082556956, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082557615, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082558269, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082558840, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082559836, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082560597, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082561622, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082562173, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082562837, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082563287, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129082563949, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754129082565114, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754129082565316, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754129082566136, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082566193, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082566866, "dur": 1339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082568205, "dur": 53766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082621975, "dur": 4838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754129082626855, "dur": 6297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754129082633154, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082633288, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082633372, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754129082634447, "dur": 336082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082529541, "dur": 19581, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082549313, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082549561, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129082549633, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082549710, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129082549824, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129082549931, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754129082550174, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129082550457, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129082550576, "dur": 440, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754129082551040, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754129082551182, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129082551309, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754129082551535, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082552364, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082552944, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082553469, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082554201, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082555357, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082556606, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082557314, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082558364, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082559235, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082560497, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082561488, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082562311, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082562817, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082563315, "dur": 1215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082564530, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082564793, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082564878, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082565023, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082565836, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082566235, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082566862, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082568201, "dur": 53713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082621920, "dur": 11157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754129082633081, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082633269, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754129082633377, "dur": 1083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754129082634488, "dur": 336122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082530123, "dur": 19247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082549386, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129082549581, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4641433ED6B9F892.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129082549664, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082549760, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082549870, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129082549985, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_974E85225CDD9232.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129082550133, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129082550533, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754129082550741, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754129082550983, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129082551150, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129082551269, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129082551398, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129082551500, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754129082551679, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082552324, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082553531, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082554178, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082554730, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082555920, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082556937, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082557548, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082558114, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082559238, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082560374, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082561249, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082562141, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082562800, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082563295, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754129082563861, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754129082564786, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082564895, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082565029, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082565845, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082566215, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082566859, "dur": 1336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082568195, "dur": 53731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082621951, "dur": 3781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754129082625734, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082625932, "dur": 6758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754129082632692, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082632916, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082633323, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082634252, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082634314, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754129082635188, "dur": 335376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082530154, "dur": 19232, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082549397, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129082549714, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_192890A86BC12540.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129082549766, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129082549877, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129082549966, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082550039, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082550301, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082550397, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082550558, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082550626, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082550703, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1754129082550966, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082551072, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082551410, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1754129082551606, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082552678, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082553460, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082554445, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082555407, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082555927, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082557451, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082558157, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082558794, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082559615, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082560390, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082561475, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082562158, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082562802, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082563330, "dur": 1199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082564529, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082564780, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Documentation.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129082565014, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082565835, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082566187, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129082566367, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129082566891, "dur": 1287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082568179, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082568608, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1754129082568774, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1754129082569240, "dur": 52726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1754129082621978, "dur": 5667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754129082627694, "dur": 6733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1754129082634485, "dur": 336100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082530193, "dur": 19213, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082549421, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129082549472, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082549643, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129082549779, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_95DEE68278F6B037.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129082550019, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1754129082550327, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129082550462, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129082550539, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129082550823, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1754129082550921, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129082551088, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129082551189, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129082551296, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1754129082551529, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082552329, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082553516, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082554405, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082555756, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082556482, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082557109, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082558296, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082559013, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082560056, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082560795, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082561801, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082561985, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082562133, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082562790, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082563269, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129082563628, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082563705, "dur": 2405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1754129082566232, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129082566375, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1754129082567614, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1754129082567719, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1754129082568227, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082568347, "dur": 53562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082621912, "dur": 6348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Documentation.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754129082628263, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082628348, "dur": 6681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1754129082635031, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1754129082635141, "dur": 335446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082530242, "dur": 19187, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082549444, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B4D1B567B4047FDB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129082549639, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082549763, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129082549987, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129082550063, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129082550182, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129082550535, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1754129082550646, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129082550772, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1754129082550932, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129082551096, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129082551188, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129082551327, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129082551468, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1754129082551640, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082552747, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082553433, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082553882, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082554661, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082556028, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082557112, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082557949, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082558831, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082559666, "dur": 1911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082561578, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082562326, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082562823, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082563271, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1754129082563756, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1754129082564649, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082564792, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082564866, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082565002, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082565823, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082566029, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082566233, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082566865, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082568182, "dur": 2402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082570585, "dur": 55210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082625797, "dur": 7166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1754129082632965, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082633178, "dur": 1003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082634270, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082635016, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1754129082635079, "dur": 335490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082530302, "dur": 19148, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082549463, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129082549623, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082549688, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129082550218, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129082550299, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129082550517, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1754129082550619, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129082550830, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1754129082551075, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.Newtonsoft.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129082551193, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129082551392, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1754129082551607, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082552510, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082553521, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082553990, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082554624, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082555798, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082556728, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082557444, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082558445, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082559224, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082560211, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082561062, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082561920, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082562104, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082562792, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082563304, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1754129082563605, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1754129082564640, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082564809, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082564872, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082564998, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082565825, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082566189, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082566846, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082568185, "dur": 53709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082621951, "dur": 5674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754129082627675, "dur": 6356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1754129082634032, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082634175, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1754129082634278, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1754129082635178, "dur": 335378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082529695, "dur": 19514, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082549216, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129082549347, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129082549472, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129082549532, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082549676, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F073B613EC8B985D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129082549842, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082549907, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1754129082550093, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1754129082550199, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129082550541, "dur": 528, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129082551138, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129082551238, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9630950048426576611.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129082551390, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1754129082551575, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082551696, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082552656, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082553163, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082554819, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\InvokeMemberDescriptor.cs"}}, {"pid": 12345, "tid": 21, "ts": 1754129082554032, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082555415, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082555994, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082556906, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082557635, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082558135, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082558877, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082559789, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082561126, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082562116, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082562793, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082563287, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129082563675, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754129082564806, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129082565054, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1754129082565771, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082565844, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082566196, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082566844, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082567443, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1754129082567579, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082568184, "dur": 53715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082621901, "dur": 4844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754129082626747, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082627023, "dur": 6877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1754129082633901, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082634035, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082634154, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1754129082634853, "dur": 335684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082530355, "dur": 19138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082549504, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129082549770, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129082549884, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129082550015, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1754129082550277, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1754129082550401, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1754129082550687, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1754129082550952, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129082551076, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129082551149, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1754129082551318, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129082551412, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129082551496, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1754129082551585, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082552264, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082552997, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082553548, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082554224, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082554636, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082555718, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082556461, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082557471, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082558330, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082558967, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082559918, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082561269, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082562210, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082562813, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082563323, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129082563872, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754129082564766, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082564892, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082565011, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082565821, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1754129082566054, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1754129082566517, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082566888, "dur": 1297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082568185, "dur": 53701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082621894, "dur": 3839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754129082625737, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082625884, "dur": 5578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1754129082631464, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082631630, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082631727, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082631980, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082632191, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082632484, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082632631, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082632722, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082633332, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082634341, "dur": 282282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1754129082916654, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754129082916627, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754129082916849, "dur": 2671, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1754129082919528, "dur": 51043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082530390, "dur": 19126, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082549530, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129082549595, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082549657, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129082549799, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129082550136, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1754129082550288, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1754129082550643, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1754129082550828, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1754129082550993, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129082551075, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129082551202, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129082551295, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129082551362, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1754129082551625, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082552336, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082553307, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082553938, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082554735, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082555853, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082556760, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082557448, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082558363, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082559199, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082559321, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082560651, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082561827, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082562170, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082562794, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082563300, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129082563514, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129082564436, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082564564, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082564812, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082564894, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082565022, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082565823, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129082566075, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129082566879, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129082566988, "dur": 1515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129082568503, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082568604, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129082568736, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129082569458, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1754129082569528, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1754129082569757, "dur": 52197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082621956, "dur": 5507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754129082627465, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082627869, "dur": 6167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1754129082634038, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082634289, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1754129082635151, "dur": 335396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082530456, "dur": 19073, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082549529, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129082549664, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082549719, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129082550121, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1754129082550316, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129082550429, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129082550803, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1754129082550930, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129082551076, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/glTFast.dots.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129082551191, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17923285327036797829.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129082551305, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129082551516, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1754129082551611, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082552832, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082553615, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082554307, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082555723, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082556804, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082557722, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082558492, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082559334, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082560176, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082561698, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082562072, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082562168, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082562791, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082563267, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129082563568, "dur": 1748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129082565317, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082565853, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129082566101, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129082566653, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129082566790, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129082567434, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/glTFast.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1754129082567541, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129082568200, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Export.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129082568572, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082568667, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/glTFast.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1754129082569086, "dur": 52852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082621939, "dur": 5969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/glTFast.Export.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754129082627910, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1754129082628019, "dur": 7202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1754129082635296, "dur": 335318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754129082981256, "dur": 1176, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 30272, "tid": 387, "ts": 1754129083000396, "dur": 1708, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 30272, "tid": 387, "ts": 1754129083002132, "dur": 1588, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 30272, "tid": 387, "ts": 1754129082997100, "dur": 7130, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}